import mysql.connector
from mysql.connector import Error

try:
    print("尝试连接到数据库...")
    connection = mysql.connector.connect(
        host='**************',
        database='devices',
        user='fms_app',
        password='B$u#kv$$zx13xz!J',
        port='3307'
    )
    if connection.is_connected():
        print("成功连接到数据库！")
except Error as e:
    print(f"数据库连接失败：{e}")
finally:
    # 确保 connection 已被定义并且是连接状态
    if 'connection' in locals() and connection.is_connected():
        connection.close()
        print("数据库连接已关闭。")
