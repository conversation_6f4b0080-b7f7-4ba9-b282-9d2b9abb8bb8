#!/bin/bash

# 启动服务脚本 - RTSP流转发服务

# 虚拟环境配置
VENV_NAME=".venv"
VENV_PYTHON="$VENV_NAME/bin/python"

# 配置参数
RTSP_URL="rtsp://admin:123456@************:554/video1"
HOST="0.0.0.0"
PORT="5000"
SCRIPT_NAME="rtsp_stream_viewer.py"

# 日志文件
LOG_FILE="rtsp_stream.log"

# PID文件，用于停止服务
PID_FILE="rtsp_stream.pid"

# 打印函数，带颜色
function print_info() {
    echo -e "\033[32m[INFO] $1\033[0m"
}

function print_warn() {
    echo -e "\033[33m[WARN] $1\033[0m"
}

function print_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查脚本是否存在
function check_script() {
    if [ ! -f $SCRIPT_NAME ]; then
        print_error "未找到脚本文件: $SCRIPT_NAME"
        print_error "请确保脚本在当前目录"
        exit 1
    fi
}

# 检查虚拟环境是否存在
function check_venv() {
    if [ ! -d $VENV_NAME ]; then
        print_error "未找到虚拟环境: $VENV_NAME"
        print_error "请先运行check_env.sh创建虚拟环境"
        exit 1
    fi
    
    if [ ! -f $VENV_PYTHON ]; then
        print_error "虚拟环境中未找到Python解释器: $VENV_PYTHON"
        print_error "虚拟环境可能已损坏，请重新创建"
        exit 1
    fi
}

# 启动服务
function start_service() {
    # 检查虚拟环境
    check_venv
    
    if [ -f $PID_FILE ]; then
        local pid=$(cat $PID_FILE)
        if ps -p $pid &> /dev/null; then
            print_warn "服务已经在运行中，PID: $pid"
            print_warn "如果需要重启，请先停止服务: ./start_service.sh stop"
            exit 1
        else
            print_warn "发现旧的PID文件，但服务未运行，删除PID文件"
            rm -f $PID_FILE
        fi
    fi

    print_info "开始启动RTSP流转发服务..."
    print_info "使用虚拟环境: $VENV_NAME"
    print_info "RTSP URL: $RTSP_URL"
    print_info "服务地址: http://$HOST:$PORT"

    # 启动服务并将输出重定向到日志文件
    $VENV_PYTHON $SCRIPT_NAME --rtsp-url "$RTSP_URL" --host $HOST --port $PORT > $LOG_FILE 2>&1 &
    local pid=$!
    echo $pid > $PID_FILE
    print_info "服务启动成功，PID: $pid"
    print_info "日志文件: $LOG_FILE"
    print_info "请访问 http://$HOST:$PORT 查看视频流"
}

# 停止服务
function stop_service() {
    if [ ! -f $PID_FILE ]; then
        print_error "未找到PID文件，服务可能未运行"
        exit 1
    fi

    local pid=$(cat $PID_FILE)
    if ps -p $pid &> /dev/null; then
        print_info "停止服务，PID: $pid"
        kill $pid
        rm -f $PID_FILE
        print_info "服务已停止"
    else
        print_warn "服务未运行，但发现PID文件，删除PID文件"
        rm -f $PID_FILE
    fi
}

# 查看服务状态
function status_service() {
    if [ ! -f $PID_FILE ]; then
        print_warn "服务未运行"
        return 1
    fi

    local pid=$(cat $PID_FILE)
    if ps -p $pid &> /dev/null; then
        print_info "服务运行中，PID: $pid"
        print_info "RTSP URL: $RTSP_URL"
        print_info "服务地址: http://$HOST:$PORT"
        return 0
    else
        print_warn "服务未运行，但发现PID文件"
        return 1
    fi
}

# 查看日志
function log_service() {
    if [ ! -f $LOG_FILE ]; then
        print_error "未找到日志文件: $LOG_FILE"
        exit 1
    fi

    print_info "查看日志文件: $LOG_FILE"
    tail -f $LOG_FILE
}

# 帮助信息
function print_help() {
    echo "用法: $0 [start|stop|status|log|help]"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  status  - 查看服务状态"
    echo "  log     - 查看服务日志"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "配置参数 (在脚本中修改):"
    echo "  RTSP_URL - RTSP流地址"
    echo "  HOST     - 服务器主机地址"
    echo "  PORT     - 服务器端口号"
}

# 主函数
function main() {
    check_script

    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        status)
            status_service
            ;;
        log)
            log_service
            ;;
        help|*)
            print_help
            ;;
    esac
}

# 执行主函数
main "$1"