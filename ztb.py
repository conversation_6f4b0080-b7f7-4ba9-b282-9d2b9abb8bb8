from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
import pandas as pd
from urllib.parse import quote

# 获取当前时间，保留到当前的时分秒
now = datetime.now()
# 计算一周前的时间，并将时间部分设置为 00:00:00
one_week_ago = (now - timedelta(days=7)).replace(hour=0, minute=0, second=0, microsecond=0)

# 格式化时间
operation_start_time = one_week_ago.strftime("%Y-%m-%d %H:%M:%S")
operation_end_time = now.strftime("%Y-%m-%d %H:%M:%S")

# 初始化变量
base_url = "https://www.xmztb.com/freecms/rest/v1/notice/selectInfoMoreChannel.do"
params = {
    "siteId": "a40f4aeb-3b75-4326-bbc9-6ebada8b71e9",
    "channel": "a4efeb63-7f73-4926-bb6e-a9928b2d2002",
    "title": "",
    "purchaser": "",
    "purchaseManner": "",
    "openTenderCode": "",
    "operationStartTime": operation_start_time,
    "operationEndTime": operation_end_time,
    "agency": "",
    "noticeType": quote("001052,00101,001031,00102,001006,001032", safe=","),  # 手动编码逗号
    "purchaseNature": "",
    "selectTimeName": "noticeTime",
    "projectSourceCode": "",
    "currPage": 1,  # 起始页码
    "pageSize": 10  # 每页数据量
}

# 构建请求对象并打印第一条请求的 URL
req = requests.Request("GET", base_url, params=params)
prepared = req.prepare()
print(f"第一条请求的 URL: {prepared.url}")

# 初始化一个空列表，用于存储所有提取的数据
all_data = []

# 初始化 total 值
total = None

# 循环请求所有分页数据
while True:
    # 发送 GET 请求
    response = requests.get(base_url, params=params)

    # 检查请求是否成功
    if response.status_code == 200:
        data = response.json()
        if data["code"] == "200" and data["data"]:
            # 如果是第一页，获取 total 值
            if params["currPage"] == 1:
                total = data.get("total", 0)
                print(f"总数据量: {total} 条")

            # 提取当前页的数据
            current_page_data = data["data"]
            for item in current_page_data:
                open_tender_code = item.get("openTenderCode", "")
                title = item.get("title", "")
                purchaser = item.get("purchaser", "")
                addtime_str = item.get("addtimeStr", "")
                pageurl = "https://www.xmztb.com" + item.get("pageurl", "")

                # 处理 description 字段，提取纯文本
                description_html = item.get("description", "")
                soup = BeautifulSoup(description_html, "html.parser")
                description_text = soup.get_text(separator=" ", strip=True)

                # 将提取的数据添加到列表中
                all_data.append({
                    "openTenderCode": open_tender_code,
                    "title": title,
                    "purchaser": purchaser,
                    "addtimeStr": addtime_str,
                    "description": description_text,
                    "pageurl": pageurl
                })

            # 打印当前请求的页码、当前页数据条数和累计数据条数
            current_page = params["currPage"]
            current_page_size = len(current_page_data)
            cumulative_size = len(all_data)
            print(f"当前请求第 {current_page} 页，共 {current_page_size} 条数据，累计 {cumulative_size} 条数据")

            # 判断是否还有更多数据
            if cumulative_size >= total:
                break  # 所有数据已获取，退出循环

            # 更新页码，请求下一页
            params["currPage"] += 1
        else:
            print("未找到数据或请求失败。")
            break
    else:
        print(f"请求失败，状态码: {response.status_code}")
        break

# 将数据转换为 DataFrame
df = pd.DataFrame(all_data)

# 导出为 Excel 文件
excel_file = "extracted_data.xlsx"
df.to_excel(excel_file, index=False, engine="openpyxl")
print(f"数据已成功导出到 {excel_file}")
