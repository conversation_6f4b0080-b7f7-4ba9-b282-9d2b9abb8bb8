('D:\\develop\\python\\test\\build\\scan\\PYZ-00.pyz',
 [('win32com',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('winerror',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('getopt', 'c:\\program files\\python\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python\\lib\\gettext.py', 'PYMODULE'),
  ('copy', 'c:\\program files\\python\\lib\\copy.py', 'PYMODULE'),
  ('struct', 'c:\\program files\\python\\lib\\struct.py', 'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('_strptime', 'c:\\program files\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'c:\\program files\\python\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'c:\\program files\\python\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'c:\\program files\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python\\lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('zipfile', 'c:\\program files\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'c:\\program files\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('ntpath', 'c:\\program files\\python\\lib\\ntpath.py', 'PYMODULE'),
  ('string', 'c:\\program files\\python\\lib\\string.py', 'PYMODULE'),
  ('genericpath', 'c:\\program files\\python\\lib\\genericpath.py', 'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'c:\\program files\\python\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'c:\\program files\\python\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib', 'c:\\program files\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('email', 'c:\\program files\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('random', 'c:\\program files\\python\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'c:\\program files\\python\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('bisect', 'c:\\program files\\python\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'c:\\program files\\python\\lib\\base64.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'c:\\program files\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'c:\\program files\\python\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'c:\\program files\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'c:\\program files\\python\\lib\\token.py', 'PYMODULE'),
  ('lzma', 'c:\\program files\\python\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'c:\\program files\\python\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'c:\\program files\\python\\lib\\bz2.py', 'PYMODULE'),
  ('stat', 'c:\\program files\\python\\lib\\stat.py', 'PYMODULE'),
  ('posixpath', 'c:\\program files\\python\\lib\\posixpath.py', 'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pickle', 'c:\\program files\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python\\lib\\pprint.py', 'PYMODULE'),
  ('doctest', 'c:\\program files\\python\\lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'c:\\program files\\python\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.mock',
   'c:\\program files\\python\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\program files\\python\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\program files\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\program files\\python\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('subprocess', 'c:\\program files\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python\\lib\\signal.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python\\lib\\selectors.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\program files\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\program files\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\program files\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'c:\\program files\\python\\lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\program files\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\program files\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\program files\\python\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\program files\\python\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\program files\\python\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\program files\\python\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\program files\\python\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('contextvars', 'c:\\program files\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('asyncio.trsock',
   'c:\\program files\\python\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\program files\\python\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('typing', 'c:\\program files\\python\\lib\\typing.py', 'PYMODULE'),
  ('asyncio.tasks',
   'c:\\program files\\python\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\program files\\python\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\program files\\python\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\program files\\python\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\program files\\python\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\program files\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\program files\\python\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\program files\\python\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\program files\\python\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\program files\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\program files\\python\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\program files\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\program files\\python\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\program files\\python\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\program files\\python\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\program files\\python\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\program files\\python\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\program files\\python\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\program files\\python\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\program files\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\program files\\python\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\program files\\python\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'c:\\program files\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'c:\\program files\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'c:\\program files\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'c:\\program files\\python\\lib\\http\\server.py',
   'PYMODULE'),
  ('http', 'c:\\program files\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('socketserver',
   'c:\\program files\\python\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes', 'c:\\program files\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.client',
   'c:\\program files\\python\\lib\\http\\client.py',
   'PYMODULE'),
  ('html', 'c:\\program files\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\program files\\python\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\program files\\python\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\program files\\python\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'c:\\program files\\python\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'c:\\program files\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('_osx_support',
   'c:\\program files\\python\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\program files\\python\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'c:\\program files\\python\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'c:\\program files\\python\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile', 'c:\\program files\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python\\lib\\gzip.py', 'PYMODULE'),
  ('distutils.dir_util',
   'c:\\program files\\python\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\program files\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\program files\\python\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\program files\\python\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\program files\\python\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\program files\\python\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'c:\\program files\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'c:\\program files\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\program files\\python\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\program files\\python\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('lib2to3.refactor',
   'c:\\program files\\python\\lib\\lib2to3\\refactor.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\program files\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\program files\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\program files\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\program files\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'c:\\program files\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'c:\\program files\\python\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\program files\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('queue', 'c:\\program files\\python\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\program files\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\program files\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\program files\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'c:\\program files\\python\\lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'c:\\program files\\python\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\program files\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\program files\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\program files\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\program files\\python\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'c:\\program files\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\program files\\python\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\program files\\python\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'c:\\program files\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\program files\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\program files\\python\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\program files\\python\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'c:\\program files\\python\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'c:\\program files\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'c:\\program files\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'c:\\program files\\python\\lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar',
   'c:\\program files\\python\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\program files\\python\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\program files\\python\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\program files\\python\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\program files\\python\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\program files\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\program files\\python\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('decimal', 'c:\\program files\\python\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python\\lib\\numbers.py', 'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\program files\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('test.support',
   'c:\\program files\\python\\lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('tracemalloc', 'c:\\program files\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tkinter',
   'c:\\program files\\python\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\program files\\python\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\program files\\python\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\program files\\python\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\program files\\python\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('test.support.testresult',
   'c:\\program files\\python\\lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\program files\\python\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('nntplib', 'c:\\program files\\python\\lib\\nntplib.py', 'PYMODULE'),
  ('logging.handlers',
   'c:\\program files\\python\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('smtplib', 'c:\\program files\\python\\lib\\smtplib.py', 'PYMODULE'),
  ('test', 'c:\\program files\\python\\lib\\test\\__init__.py', 'PYMODULE'),
  ('multiprocessing.process',
   'c:\\program files\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('lib2to3.btm_matcher',
   'c:\\program files\\python\\lib\\lib2to3\\btm_matcher.py',
   'PYMODULE'),
  ('lib2to3.btm_utils',
   'c:\\program files\\python\\lib\\lib2to3\\btm_utils.py',
   'PYMODULE'),
  ('lib2to3.pgen2.grammar',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\grammar.py',
   'PYMODULE'),
  ('lib2to3.pygram',
   'c:\\program files\\python\\lib\\lib2to3\\pygram.py',
   'PYMODULE'),
  ('lib2to3.pytree',
   'c:\\program files\\python\\lib\\lib2to3\\pytree.py',
   'PYMODULE'),
  ('lib2to3',
   'c:\\program files\\python\\lib\\lib2to3\\__init__.py',
   'PYMODULE'),
  ('lib2to3.patcomp',
   'c:\\program files\\python\\lib\\lib2to3\\patcomp.py',
   'PYMODULE'),
  ('lib2to3.pgen2.parse',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\parse.py',
   'PYMODULE'),
  ('lib2to3.pgen2.literals',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\literals.py',
   'PYMODULE'),
  ('lib2to3.fixer_util',
   'c:\\program files\\python\\lib\\lib2to3\\fixer_util.py',
   'PYMODULE'),
  ('lib2to3.pgen2.token',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\token.py',
   'PYMODULE'),
  ('lib2to3.pgen2.tokenize',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\tokenize.py',
   'PYMODULE'),
  ('lib2to3.pgen2.driver',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\driver.py',
   'PYMODULE'),
  ('lib2to3.pgen2.pgen',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\pgen.py',
   'PYMODULE'),
  ('lib2to3.pgen2',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\__init__.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\program files\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\program files\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('platform', 'c:\\program files\\python\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'c:\\program files\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('pkgutil', 'c:\\program files\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'c:\\program files\\python\\lib\\zipimport.py', 'PYMODULE'),
  ('runpy', 'c:\\program files\\python\\lib\\runpy.py', 'PYMODULE'),
  ('shlex', 'c:\\program files\\python\\lib\\shlex.py', 'PYMODULE'),
  ('code', 'c:\\program files\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\program files\\python\\lib\\codeop.py', 'PYMODULE'),
  ('dis', 'c:\\program files\\python\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'c:\\program files\\python\\lib\\opcode.py', 'PYMODULE'),
  ('bdb', 'c:\\program files\\python\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'c:\\program files\\python\\lib\\cmd.py', 'PYMODULE'),
  ('inspect', 'c:\\program files\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'c:\\program files\\python\\lib\\ast.py', 'PYMODULE'),
  ('difflib', 'c:\\program files\\python\\lib\\difflib.py', 'PYMODULE'),
  ('__future__', 'c:\\program files\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('glob', 'c:\\program files\\python\\lib\\glob.py', 'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('pywintypes',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('imp', 'c:\\program files\\python\\lib\\imp.py', 'PYMODULE'),
  ('pythoncom',
   'c:\\program files\\python\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('tempfile', 'c:\\program files\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python\\lib\\shutil.py', 'PYMODULE'),
  ('os', 'c:\\program files\\python\\lib\\os.py', 'PYMODULE'),
  ('pkg_resources',
   'c:\\program files\\python\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.six',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\six.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid', 'c:\\program files\\python\\lib\\uuid.py', 'PYMODULE'),
  ('netbios',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources.py31compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('mysql.connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\__init__.py',
   'PYMODULE'),
  ('mysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.pooling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\pooling.py',
   'PYMODULE'),
  ('mysql.connector.abstracts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\abstracts.py',
   'PYMODULE'),
  ('mysql.connector.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\utils.py',
   'PYMODULE'),
  ('mysql.connector.custom_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\custom_types.py',
   'PYMODULE'),
  ('mysql.connector.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\types.py',
   'PYMODULE'),
  ('mysql.connector.tls_ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\tls_ciphers.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\instrumentation.py',
   'PYMODULE'),
  ('mysql.connector.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\logger.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\constants.py',
   'PYMODULE'),
  ('mysql.connector.conversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\conversion.py',
   'PYMODULE'),
  ('mysql.connector.optionfiles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\optionfiles.py',
   'PYMODULE'),
  ('dns.resolver',
   'c:\\program files\\python\\lib\\site-packages\\dns\\resolver.py',
   'PYMODULE'),
  ('dns',
   'c:\\program files\\python\\lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns.version',
   'c:\\program files\\python\\lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('dns.tsig',
   'c:\\program files\\python\\lib\\site-packages\\dns\\tsig.py',
   'PYMODULE'),
  ('dns.reversename',
   'c:\\program files\\python\\lib\\site-packages\\dns\\reversename.py',
   'PYMODULE'),
  ('dns.rdatatype',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdatatype.py',
   'PYMODULE'),
  ('dns.enum',
   'c:\\program files\\python\\lib\\site-packages\\dns\\enum.py',
   'PYMODULE'),
  ('dns.rdataclass',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdataclass.py',
   'PYMODULE'),
  ('dns.rcode',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rcode.py',
   'PYMODULE'),
  ('dns.query',
   'c:\\program files\\python\\lib\\site-packages\\dns\\query.py',
   'PYMODULE'),
  ('requests',
   'c:\\program files\\python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\program files\\python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\program files\\python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\program files\\python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'c:\\program files\\python\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('json', 'c:\\program files\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\program files\\python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\program files\\python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\program files\\python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('idna',
   'c:\\program files\\python\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\program files\\python\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\program files\\python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\program files\\python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\program files\\python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\program files\\python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('ipaddress', 'c:\\program files\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.request',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\program files\\python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\program files\\python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\program files\\python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\program files\\python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\program files\\python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\program files\\python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\program files\\python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\program files\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\program files\\python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('fractions', 'c:\\program files\\python\\lib\\fractions.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('six', 'c:\\program files\\python\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('cryptography.hazmat._der',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\_der.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.cmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'c:\\program files\\python\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'c:\\program files\\python\\lib\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.interfaces',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\interfaces.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.encode_asn1',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\encode_asn1.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.x509.ocsp',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\ocsp.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ocsp',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ocsp.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x509',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x509.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('chardet',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('dns.serial',
   'c:\\program files\\python\\lib\\site-packages\\dns\\serial.py',
   'PYMODULE'),
  ('dns.name',
   'c:\\program files\\python\\lib\\site-packages\\dns\\name.py',
   'PYMODULE'),
  ('dns.wire',
   'c:\\program files\\python\\lib\\site-packages\\dns\\wire.py',
   'PYMODULE'),
  ('dns.message',
   'c:\\program files\\python\\lib\\site-packages\\dns\\message.py',
   'PYMODULE'),
  ('dns.update',
   'c:\\program files\\python\\lib\\site-packages\\dns\\update.py',
   'PYMODULE'),
  ('dns.rdataset',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdataset.py',
   'PYMODULE'),
  ('dns.set',
   'c:\\program files\\python\\lib\\site-packages\\dns\\set.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\OPT.py',
   'PYMODULE'),
  ('dns.renderer',
   'c:\\program files\\python\\lib\\site-packages\\dns\\renderer.py',
   'PYMODULE'),
  ('dns.rrset',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rrset.py',
   'PYMODULE'),
  ('dns.rdata',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdata.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\RRSIG.py',
   'PYMODULE'),
  ('dns.dnssec',
   'c:\\program files\\python\\lib\\site-packages\\dns\\dnssec.py',
   'PYMODULE'),
  ('dns.node',
   'c:\\program files\\python\\lib\\site-packages\\dns\\node.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\dsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\CH\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\CH\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\APL.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\AFSDB.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\KX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\RP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\PX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\util.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\SOA.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\dnskeybase.py',
   'PYMODULE'),
  ('dns.tokenizer',
   'c:\\program files\\python\\lib\\site-packages\\dns\\tokenizer.py',
   'PYMODULE'),
  ('dns.ttl',
   'c:\\program files\\python\\lib\\site-packages\\dns\\ttl.py',
   'PYMODULE'),
  ('dns.entropy',
   'c:\\program files\\python\\lib\\site-packages\\dns\\entropy.py',
   'PYMODULE'),
  ('dns.opcode',
   'c:\\program files\\python\\lib\\site-packages\\dns\\opcode.py',
   'PYMODULE'),
  ('dns.edns',
   'c:\\program files\\python\\lib\\site-packages\\dns\\edns.py',
   'PYMODULE'),
  ('dns.ipv6',
   'c:\\program files\\python\\lib\\site-packages\\dns\\ipv6.py',
   'PYMODULE'),
  ('dns.ipv4',
   'c:\\program files\\python\\lib\\site-packages\\dns\\ipv4.py',
   'PYMODULE'),
  ('dns.inet',
   'c:\\program files\\python\\lib\\site-packages\\dns\\inet.py',
   'PYMODULE'),
  ('dns.flags',
   'c:\\program files\\python\\lib\\site-packages\\dns\\flags.py',
   'PYMODULE'),
  ('dummy_threading',
   'c:\\program files\\python\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\program files\\python\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('dns.exception',
   'c:\\program files\\python\\lib\\site-packages\\dns\\exception.py',
   'PYMODULE'),
  ('mysql.connector.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\errors.py',
   'PYMODULE'),
  ('mysql.connector.locales',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\locales\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.dbapi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\dbapi.py',
   'PYMODULE'),
  ('mysql.connector.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\constants.py',
   'PYMODULE'),
  ('mysql.connector.charsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\charsets.py',
   'PYMODULE'),
  ('mysql.connector.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\connection.py',
   'PYMODULE'),
  ('mysql.connector.protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\protocol.py',
   'PYMODULE'),
  ('mysql.connector.plugins.caching_sha2_password',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\caching_sha2_password.py',
   'PYMODULE'),
  ('mysql.connector.plugins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.context_propagation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\context_propagation.py',
   'PYMODULE'),
  ('mysql.connector.network',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\network.py',
   'PYMODULE'),
  ('mysql.connector.cursor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\cursor.py',
   'PYMODULE'),
  ('mysql.connector.authentication',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\authentication.py',
   'PYMODULE'),
  ('mysql.connector.connection_cext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\connection_cext.py',
   'PYMODULE'),
  ('mysql.connector.cursor_cext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\cursor_cext.py',
   'PYMODULE'),
  ('mysql.connector.errorcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\errorcode.py',
   'PYMODULE'),
  ('mysql.connector.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\version.py',
   'PYMODULE'),
  ('datetime', 'c:\\program files\\python\\lib\\datetime.py', 'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pytz',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('dataclasses', 'c:\\program files\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('markupsafe._constants',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\_constants.py',
   'PYMODULE'),
  ('markupsafe._compat',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\_compat.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.processors',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.pool.dbapi_proxy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\pool\\dbapi_proxy.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.interfaces',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.events',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.strategies',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.threadlocal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\threadlocal.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlite3',
   'c:\\program files\\python\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'c:\\program files\\python\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'c:\\program files\\python\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pysybase',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pysybase.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.zxJDBC',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\zxJDBC.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pypostgresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pypostgresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pygresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pygresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.oursql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\oursql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.gaerdbms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\gaerdbms.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.mxodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.mxodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.adodbapi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\adodbapi.py',
   'PYMODULE'),
  ('adodbapi',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\__init__.py',
   'PYMODULE'),
  ('adodbapi.adodbapi',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\adodbapi.py',
   'PYMODULE'),
  ('adodbapi.apibase',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\apibase.py',
   'PYMODULE'),
  ('adodbapi.process_connect_string',
   'c:\\program '
   'files\\python\\lib\\site-packages\\adodbapi\\process_connect_string.py',
   'PYMODULE'),
  ('adodbapi.is64bit',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\is64bit.py',
   'PYMODULE'),
  ('adodbapi.ado_consts',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\ado_consts.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.kinterbasdb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\kinterbasdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.fdb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\fdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\base.py',
   'PYMODULE'),
  ('MySQLdb',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.connections',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\connections.py',
   'PYMODULE'),
  ('MySQLdb.converters',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\converters.py',
   'PYMODULE'),
  ('MySQLdb.constants.FLAG',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\constants\\FLAG.py',
   'PYMODULE'),
  ('MySQLdb.constants.CLIENT',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\CLIENT.py',
   'PYMODULE'),
  ('MySQLdb._exceptions',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\_exceptions.py',
   'PYMODULE'),
  ('MySQLdb.cursors',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\cursors.py',
   'PYMODULE'),
  ('MySQLdb.times',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\times.py',
   'PYMODULE'),
  ('MySQLdb.constants.FIELD_TYPE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('MySQLdb.constants',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.release',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\release.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('lxml',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\program files\\python\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\program files\\python\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\program files\\python\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\program files\\python\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\program files\\python\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\program files\\python\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\program files\\python\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'c:\\program files\\python\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\fcompiler\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.config_compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\config_compiler.py',
   'PYMODULE'),
  ('numpy.distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.extension',
   'c:\\program files\\python\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.py31compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py31compat.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('distutils.cmd',
   'c:\\program files\\python\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.py33compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py33compat.py',
   'PYMODULE'),
  ('html.parser',
   'c:\\program files\\python\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase', 'c:\\program files\\python\\lib\\_markupbase.py', 'PYMODULE'),
  ('setuptools.ssl_support',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.py27compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py27compat.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('site',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\fake-modules\\site.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'c:\\program files\\python\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command',
   'c:\\program files\\python\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.install',
   'c:\\program files\\python\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'c:\\program files\\python\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'c:\\program files\\python\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'c:\\program files\\python\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.six',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_vendor\\six.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'c:\\program files\\python\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'c:\\program files\\python\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'c:\\program files\\python\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools.namespaces',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\namespaces.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools.extern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.develop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\develop.py',
   'PYMODULE'),
  ('setuptools.command.develop',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\develop.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_clib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('distutils.command.bdist_rpm',
   'c:\\program files\\python\\lib\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools.command.bdist_rpm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('numpy.distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools.command.install',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\install.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('distutils.command.install_headers',
   'c:\\program files\\python\\lib\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('distutils.command.install_data',
   'c:\\program files\\python\\lib\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('numpy.distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('numpy.distutils.ccompiler_opt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\ccompiler_opt.py',
   'PYMODULE'),
  ('distutils.command.build_clib',
   'c:\\program files\\python\\lib\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('numpy.distutils.system_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\system_info.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_src',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_src.py',
   'PYMODULE'),
  ('numpy.distutils.conv_template',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\conv_template.py',
   'PYMODULE'),
  ('numpy.distutils.from_template',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\from_template.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput', 'c:\\program files\\python\\lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('distutils.command.build_py',
   'c:\\program files\\python\\lib\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('numpy.distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'c:\\program files\\python\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('numpy.distutils.command.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\config.py',
   'PYMODULE'),
  ('numpy.distutils.command.autodist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\autodist.py',
   'PYMODULE'),
  ('numpy.distutils.mingw32ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\mingw32ccompiler.py',
   'PYMODULE'),
  ('numpy.distutils.lib2def',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\lib2def.py',
   'PYMODULE'),
  ('distutils.msvccompiler',
   'c:\\program files\\python\\lib\\distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'c:\\program files\\python\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.cygwinccompiler',
   'c:\\program files\\python\\lib\\distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.command.config',
   'c:\\program files\\python\\lib\\distutils\\command\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'c:\\program files\\python\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'c:\\program files\\python\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'c:\\program files\\python\\lib\\cgi.py', 'PYMODULE'),
  ('distutils.dist',
   'c:\\program files\\python\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'c:\\program files\\python\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('numpy.distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\core.py',
   'PYMODULE'),
  ('numpy.distutils.numpy_distribution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\numpy_distribution.py',
   'PYMODULE'),
  ('numpy.distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\extension.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\fcompiler\\environment.py',
   'PYMODULE'),
  ('numpy.distutils._shell_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\_shell_utils.py',
   'PYMODULE'),
  ('pipes', 'c:\\program files\\python\\lib\\pipes.py', 'PYMODULE'),
  ('numpy.distutils.misc_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\misc_util.py',
   'PYMODULE'),
  ('numpy.distutils.npy_pkg_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\npy_pkg_config.py',
   'PYMODULE'),
  ('curses', 'c:\\program files\\python\\lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key',
   'c:\\program files\\python\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('numpy.distutils.exec_command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\exec_command.py',
   'PYMODULE'),
  ('numpy.distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.version',
   'c:\\program files\\python\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._psaix',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psaix.py',
   'PYMODULE'),
  ('psutil._pssunos',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pssunos.py',
   'PYMODULE'),
  ('psutil._psbsd',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psbsd.py',
   'PYMODULE'),
  ('psutil._psosx',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psosx.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._pslinux',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pslinux.py',
   'PYMODULE'),
  ('psutil._psposix',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psposix.py',
   'PYMODULE'),
  ('psutil._compat',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\__config__.py',
   'PYMODULE'),
  ('numpy.distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\program files\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\program files\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\program files\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\program files\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\program files\\python\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('socket', 'c:\\program files\\python\\lib\\socket.py', 'PYMODULE'),
  ('scapy.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\all.py',
   'PYMODULE'),
  ('scapy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\__init__.py',
   'PYMODULE'),
  ('scapy.ansmachine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\ansmachine.py',
   'PYMODULE'),
  ('scapy.route6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\route6.py',
   'PYMODULE'),
  ('scapy.pton_ntop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\pton_ntop.py',
   'PYMODULE'),
  ('scapy.utils6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\utils6.py',
   'PYMODULE'),
  ('scapy.scapypipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\scapypipes.py',
   'PYMODULE'),
  ('scapy.layers.inet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\inet.py',
   'PYMODULE'),
  ('scapy.layers.inet6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\inet6.py',
   'PYMODULE'),
  ('scapy.layers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\__init__.py',
   'PYMODULE'),
  ('scapy.libs.matplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\matplot.py',
   'PYMODULE'),
  ('scapy.libs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.l2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\l2.py',
   'PYMODULE'),
  ('scapy.pipetool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\pipetool.py',
   'PYMODULE'),
  ('scapy.asn1.mib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\mib.py',
   'PYMODULE'),
  ('scapy.asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\__init__.py',
   'PYMODULE'),
  ('scapy.asn1.ber',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\ber.py',
   'PYMODULE'),
  ('scapy.asn1.asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\asn1.py',
   'PYMODULE'),
  ('scapy.layers.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\all.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_nrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_nrpc.py',
   'PYMODULE'),
  ('scapy.layers.dhcp6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dhcp6.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton_srv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\automaton_srv.py',
   'PYMODULE'),
  ('scapy.layers.ntlm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ntlm.py',
   'PYMODULE'),
  ('scapy.layers.gprs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\gprs.py',
   'PYMODULE'),
  ('scapy.layers.vrrp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\vrrp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_block',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_block.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.pkcs1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\pkcs1.py',
   'PYMODULE'),
  ('scapy.layers.dot11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dot11.py',
   'PYMODULE'),
  ('scapy.layers.dcerpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dcerpc.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.common_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\common_types.py',
   'PYMODULE'),
  ('scapy.contrib.rtps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\__init__.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.pid_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\pid_types.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.rtps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\rtps.py',
   'PYMODULE'),
  ('scapy.contrib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tftp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tftp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.compression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\compression.py',
   'PYMODULE'),
  ('scapy.layers.bluetooth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\bluetooth.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_stream.py',
   'PYMODULE'),
  ('scapy.layers.dot15d4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dot15d4.py',
   'PYMODULE'),
  ('scapy.layers.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\http.py',
   'PYMODULE'),
  ('scapy.contrib.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\http2.py',
   'PYMODULE'),
  ('scapy.layers.tls.handshake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\handshake.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.dns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dns.py',
   'PYMODULE'),
  ('scapy.layers.rtp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\rtp.py',
   'PYMODULE'),
  ('scapy.layers.netflow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\netflow.py',
   'PYMODULE'),
  ('scapy.layers.llmnr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\llmnr.py',
   'PYMODULE'),
  ('scapy.layers.can',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\can.py',
   'PYMODULE'),
  ('scapy.layers.usb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\usb.py',
   'PYMODULE'),
  ('scapy.layers.tls.keyexchange_tls13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\keyexchange_tls13.py',
   'PYMODULE'),
  ('scapy.layers.lltd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\lltd.py',
   'PYMODULE'),
  ('scapy.layers.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ssh.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.groups',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\groups.py',
   'PYMODULE'),
  ('scapy.layers.bluetooth4LE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\bluetooth4LE.py',
   'PYMODULE'),
  ('scapy.contrib.ethercat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\ethercat.py',
   'PYMODULE'),
  ('scapy.layers.snmp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\snmp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.hash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\hash.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\ciphers.py',
   'PYMODULE'),
  ('scapy.layers.tls.cert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\cert.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.msdrsr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\msdrsr.py',
   'PYMODULE'),
  ('scapy.layers.zigbee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\zigbee.py',
   'PYMODULE'),
  ('scapy.layers.eap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\eap.py',
   'PYMODULE'),
  ('scapy.layers.tls.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\extensions.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_wkst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_wkst.py',
   'PYMODULE'),
  ('scapy.layers.netbios',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\netbios.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.h_mac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\h_mac.py',
   'PYMODULE'),
  ('scapy.layers.mgcp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\mgcp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\common.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.prf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\prf.py',
   'PYMODULE'),
  ('scapy.layers.sctp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\sctp.py',
   'PYMODULE'),
  ('scapy.layers.tls.handshake_sslv2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\handshake_sslv2.py',
   'PYMODULE'),
  ('scapy.layers.hsrp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\hsrp.py',
   'PYMODULE'),
  ('scapy.layers.radius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\radius.py',
   'PYMODULE'),
  ('scapy.layers.tls.record',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\record.py',
   'PYMODULE'),
  ('scapy.layers.kerberos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\kerberos.py',
   'PYMODULE'),
  ('scapy.libs.rfc3961',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\rfc3961.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('scapy.layers.ir',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ir.py',
   'PYMODULE'),
  ('scapy.layers.tls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.kx_algs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\kx_algs.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.md4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\md4.py',
   'PYMODULE'),
  ('scapy.layers.tls.keyexchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\keyexchange.py',
   'PYMODULE'),
  ('scapy.layers.mobileip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\mobileip.py',
   'PYMODULE'),
  ('scapy.layers.rip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\rip.py',
   'PYMODULE'),
  ('scapy.layers.msrpce',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tls.record_tls13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\record_tls13.py',
   'PYMODULE'),
  ('scapy.layers.tls.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\all.py',
   'PYMODULE'),
  ('scapy.layers.l2tp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\l2tp.py',
   'PYMODULE'),
  ('scapy.layers.tls.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\tools.py',
   'PYMODULE'),
  ('scapy.layers.gssapi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\gssapi.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\automaton.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_samr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_samr.py',
   'PYMODULE'),
  ('scapy.layers.dhcp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dhcp.py',
   'PYMODULE'),
  ('scapy.layers.tls.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\session.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_srvs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_srvs.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton_cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\automaton_cli.py',
   'PYMODULE'),
  ('scapy.layers.smbserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smbserver.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.suites',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\suites.py',
   'PYMODULE'),
  ('scapy.layers.tls.basefields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\basefields.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_aead.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.ept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\ept.py',
   'PYMODULE'),
  ('scapy.layers.spnego',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\spnego.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ept.py',
   'PYMODULE'),
  ('scapy.layers.clns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\clns.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.mspac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\mspac.py',
   'PYMODULE'),
  ('scapy.layers.sixlowpan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\sixlowpan.py',
   'PYMODULE'),
  ('scapy.layers.ipsec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ipsec.py',
   'PYMODULE'),
  ('scapy.layers.pptp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\pptp.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_dcom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_dcom.py',
   'PYMODULE'),
  ('scapy.layers.tuntap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tuntap.py',
   'PYMODULE'),
  ('scapy.layers.isakmp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\isakmp.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\all.py',
   'PYMODULE'),
  ('scapy.layers.skinny',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\skinny.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.rpcserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\rpcserver.py',
   'PYMODULE'),
  ('scapy.layers.smb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smb.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.hkdf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\hkdf.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.hkdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\hkdf.py',
   'PYMODULE'),
  ('scapy.layers.vxlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\vxlan.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.rpcclient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\rpcclient.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_drsr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_drsr.py',
   'PYMODULE'),
  ('scapy.layers.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\x509.py',
   'PYMODULE'),
  ('scapy.layers.ldap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ldap.py',
   'PYMODULE'),
  ('scapy.layers.smbclient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smbclient.py',
   'PYMODULE'),
  ('scapy.layers.tls.record_sslv2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\record_sslv2.py',
   'PYMODULE'),
  ('scapy.layers.ppp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ppp.py',
   'PYMODULE'),
  ('scapy.layers.pflog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\pflog.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.msnrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\msnrpc.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.msdcom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\msdcom.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\all.py',
   'PYMODULE'),
  ('scapy.layers.ppi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ppi.py',
   'PYMODULE'),
  ('scapy.layers.smb2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smb2.py',
   'PYMODULE'),
  ('scapy.layers.ntp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ntp.py',
   'PYMODULE'),
  ('scapy.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\compat.py',
   'PYMODULE'),
  ('scapy.consts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\consts.py',
   'PYMODULE'),
  ('scapy.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\main.py',
   'PYMODULE'),
  ('rlcompleter', 'c:\\program files\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('scapy.autorun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\autorun.py',
   'PYMODULE'),
  ('scapy.automaton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\automaton.py',
   'PYMODULE'),
  ('scapy.as_resolvers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\as_resolvers.py',
   'PYMODULE'),
  ('scapy.volatile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\volatile.py',
   'PYMODULE'),
  ('scapy.supersocket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\supersocket.py',
   'PYMODULE'),
  ('scapy.arch.linux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\linux\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.linux.rtnetlink',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\linux\\rtnetlink.py',
   'PYMODULE'),
  ('scapy.libs.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\structures.py',
   'PYMODULE'),
  ('scapy.arch.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\common.py',
   'PYMODULE'),
  ('scapy.libs.winpcapy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\winpcapy.py',
   'PYMODULE'),
  ('scapy.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\sessions.py',
   'PYMODULE'),
  ('scapy.sendrecv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\sendrecv.py',
   'PYMODULE'),
  ('scapy.route',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\route.py',
   'PYMODULE'),
  ('scapy.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\utils.py',
   'PYMODULE'),
  ('scapy.arch.windows.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\windows\\structures.py',
   'PYMODULE'),
  ('scapy.arch.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\windows\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.libpcap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\libpcap.py',
   'PYMODULE'),
  ('scapy.arch.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\unix.py',
   'PYMODULE'),
  ('scapy.asn1packet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1packet.py',
   'PYMODULE'),
  ('scapy.asn1fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1fields.py',
   'PYMODULE'),
  ('scapy.packet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\packet.py',
   'PYMODULE'),
  ('scapy.libs.test_pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\test_pyx.py',
   'PYMODULE'),
  ('scapy.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\fields.py',
   'PYMODULE'),
  ('scapy.plist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\plist.py',
   'PYMODULE'),
  ('scapy.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\interfaces.py',
   'PYMODULE'),
  ('scapy.arch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.windows.native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\windows\\native.py',
   'PYMODULE'),
  ('scapy.arch.solaris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\solaris.py',
   'PYMODULE'),
  ('scapy.arch.bpf.supersocket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\supersocket.py',
   'PYMODULE'),
  ('scapy.arch.bpf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.bpf.consts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\consts.py',
   'PYMODULE'),
  ('scapy.arch.bpf.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\core.py',
   'PYMODULE'),
  ('scapy.arch.bpf.pfroute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\pfroute.py',
   'PYMODULE'),
  ('scapy.libs.extcap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\extcap.py',
   'PYMODULE'),
  ('scapy.themes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\themes.py',
   'PYMODULE'),
  ('scapy.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\error.py',
   'PYMODULE'),
  ('colorama',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('scapy.data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\data.py',
   'PYMODULE'),
  ('scapy.libs.manuf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\manuf.py',
   'PYMODULE'),
  ('scapy.libs.ethertypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\ethertypes.py',
   'PYMODULE'),
  ('scapy.dadict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\dadict.py',
   'PYMODULE'),
  ('scapy.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\config.py',
   'PYMODULE'),
  ('scapy.modules.nmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\modules\\nmap.py',
   'PYMODULE'),
  ('scapy.modules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\modules\\__init__.py',
   'PYMODULE'),
  ('scapy.base_classes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\base_classes.py',
   'PYMODULE')])
