(['D:\\develop\\python\\test\\scan.py'],
 ['D:\\develop\\python\\test', 'D:\\develop\\python\\test'],
 ['codecs'],
 [],
 [],
 [],
 False,
 False,
 '3.8.6 (tags/v3.8.6:db45529, Sep 23 2020, 15:52:53) [MSC v.1927 64 bit '
 '(AMD64)]',
 [('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_certifi',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_certifi.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('scan', 'D:\\develop\\python\\test\\scan.py', 'PYSOURCE')],
 [('win32com',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('winerror',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('getopt', 'c:\\program files\\python\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python\\lib\\gettext.py', 'PYMODULE'),
  ('copy', 'c:\\program files\\python\\lib\\copy.py', 'PYMODULE'),
  ('struct', 'c:\\program files\\python\\lib\\struct.py', 'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('_strptime', 'c:\\program files\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'c:\\program files\\python\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'c:\\program files\\python\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'c:\\program files\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python\\lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('zipfile', 'c:\\program files\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'c:\\program files\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('ntpath', 'c:\\program files\\python\\lib\\ntpath.py', 'PYMODULE'),
  ('string', 'c:\\program files\\python\\lib\\string.py', 'PYMODULE'),
  ('genericpath', 'c:\\program files\\python\\lib\\genericpath.py', 'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'c:\\program files\\python\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'c:\\program files\\python\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib', 'c:\\program files\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('email', 'c:\\program files\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('random', 'c:\\program files\\python\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'c:\\program files\\python\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('bisect', 'c:\\program files\\python\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'c:\\program files\\python\\lib\\base64.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'c:\\program files\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'c:\\program files\\python\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'c:\\program files\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'c:\\program files\\python\\lib\\token.py', 'PYMODULE'),
  ('lzma', 'c:\\program files\\python\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'c:\\program files\\python\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'c:\\program files\\python\\lib\\bz2.py', 'PYMODULE'),
  ('stat', 'c:\\program files\\python\\lib\\stat.py', 'PYMODULE'),
  ('posixpath', 'c:\\program files\\python\\lib\\posixpath.py', 'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pickle', 'c:\\program files\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python\\lib\\pprint.py', 'PYMODULE'),
  ('doctest', 'c:\\program files\\python\\lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'c:\\program files\\python\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.mock',
   'c:\\program files\\python\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\program files\\python\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\program files\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\program files\\python\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('subprocess', 'c:\\program files\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python\\lib\\signal.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python\\lib\\selectors.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\program files\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\program files\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\program files\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'c:\\program files\\python\\lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\program files\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\program files\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\program files\\python\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\program files\\python\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\program files\\python\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\program files\\python\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\program files\\python\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('contextvars', 'c:\\program files\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('asyncio.trsock',
   'c:\\program files\\python\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\program files\\python\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('typing', 'c:\\program files\\python\\lib\\typing.py', 'PYMODULE'),
  ('asyncio.tasks',
   'c:\\program files\\python\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\program files\\python\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\program files\\python\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\program files\\python\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\program files\\python\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\program files\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\program files\\python\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\program files\\python\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\program files\\python\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\program files\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\program files\\python\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\program files\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\program files\\python\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\program files\\python\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\program files\\python\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\program files\\python\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\program files\\python\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\program files\\python\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\program files\\python\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\program files\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\program files\\python\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\program files\\python\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'c:\\program files\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'c:\\program files\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'c:\\program files\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'c:\\program files\\python\\lib\\http\\server.py',
   'PYMODULE'),
  ('http', 'c:\\program files\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('socketserver',
   'c:\\program files\\python\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes', 'c:\\program files\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.client',
   'c:\\program files\\python\\lib\\http\\client.py',
   'PYMODULE'),
  ('html', 'c:\\program files\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\program files\\python\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\program files\\python\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\program files\\python\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'c:\\program files\\python\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'c:\\program files\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('_osx_support',
   'c:\\program files\\python\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\program files\\python\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'c:\\program files\\python\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'c:\\program files\\python\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile', 'c:\\program files\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python\\lib\\gzip.py', 'PYMODULE'),
  ('distutils.dir_util',
   'c:\\program files\\python\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\program files\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\program files\\python\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\program files\\python\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\program files\\python\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\program files\\python\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'c:\\program files\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'c:\\program files\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\program files\\python\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\program files\\python\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('lib2to3.refactor',
   'c:\\program files\\python\\lib\\lib2to3\\refactor.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\program files\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\program files\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\program files\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\program files\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'c:\\program files\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'c:\\program files\\python\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\program files\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('queue', 'c:\\program files\\python\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\program files\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\program files\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\program files\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'c:\\program files\\python\\lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'c:\\program files\\python\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\program files\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\program files\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\program files\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\program files\\python\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'c:\\program files\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\program files\\python\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\program files\\python\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'c:\\program files\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\program files\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\program files\\python\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\program files\\python\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'c:\\program files\\python\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'c:\\program files\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'c:\\program files\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'c:\\program files\\python\\lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar',
   'c:\\program files\\python\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\program files\\python\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\program files\\python\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\program files\\python\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\program files\\python\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\program files\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\program files\\python\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('decimal', 'c:\\program files\\python\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python\\lib\\numbers.py', 'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\program files\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('test.support',
   'c:\\program files\\python\\lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('tracemalloc', 'c:\\program files\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tkinter',
   'c:\\program files\\python\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\program files\\python\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\program files\\python\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\program files\\python\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\program files\\python\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('test.support.testresult',
   'c:\\program files\\python\\lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\program files\\python\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('nntplib', 'c:\\program files\\python\\lib\\nntplib.py', 'PYMODULE'),
  ('logging.handlers',
   'c:\\program files\\python\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('smtplib', 'c:\\program files\\python\\lib\\smtplib.py', 'PYMODULE'),
  ('test', 'c:\\program files\\python\\lib\\test\\__init__.py', 'PYMODULE'),
  ('multiprocessing.process',
   'c:\\program files\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('lib2to3.btm_matcher',
   'c:\\program files\\python\\lib\\lib2to3\\btm_matcher.py',
   'PYMODULE'),
  ('lib2to3.btm_utils',
   'c:\\program files\\python\\lib\\lib2to3\\btm_utils.py',
   'PYMODULE'),
  ('lib2to3.pgen2.grammar',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\grammar.py',
   'PYMODULE'),
  ('lib2to3.pygram',
   'c:\\program files\\python\\lib\\lib2to3\\pygram.py',
   'PYMODULE'),
  ('lib2to3.pytree',
   'c:\\program files\\python\\lib\\lib2to3\\pytree.py',
   'PYMODULE'),
  ('lib2to3',
   'c:\\program files\\python\\lib\\lib2to3\\__init__.py',
   'PYMODULE'),
  ('lib2to3.patcomp',
   'c:\\program files\\python\\lib\\lib2to3\\patcomp.py',
   'PYMODULE'),
  ('lib2to3.pgen2.parse',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\parse.py',
   'PYMODULE'),
  ('lib2to3.pgen2.literals',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\literals.py',
   'PYMODULE'),
  ('lib2to3.fixer_util',
   'c:\\program files\\python\\lib\\lib2to3\\fixer_util.py',
   'PYMODULE'),
  ('lib2to3.pgen2.token',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\token.py',
   'PYMODULE'),
  ('lib2to3.pgen2.tokenize',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\tokenize.py',
   'PYMODULE'),
  ('lib2to3.pgen2.driver',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\driver.py',
   'PYMODULE'),
  ('lib2to3.pgen2.pgen',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\pgen.py',
   'PYMODULE'),
  ('lib2to3.pgen2',
   'c:\\program files\\python\\lib\\lib2to3\\pgen2\\__init__.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\program files\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\program files\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('platform', 'c:\\program files\\python\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'c:\\program files\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('pkgutil', 'c:\\program files\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'c:\\program files\\python\\lib\\zipimport.py', 'PYMODULE'),
  ('runpy', 'c:\\program files\\python\\lib\\runpy.py', 'PYMODULE'),
  ('shlex', 'c:\\program files\\python\\lib\\shlex.py', 'PYMODULE'),
  ('code', 'c:\\program files\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\program files\\python\\lib\\codeop.py', 'PYMODULE'),
  ('dis', 'c:\\program files\\python\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'c:\\program files\\python\\lib\\opcode.py', 'PYMODULE'),
  ('bdb', 'c:\\program files\\python\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'c:\\program files\\python\\lib\\cmd.py', 'PYMODULE'),
  ('inspect', 'c:\\program files\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'c:\\program files\\python\\lib\\ast.py', 'PYMODULE'),
  ('difflib', 'c:\\program files\\python\\lib\\difflib.py', 'PYMODULE'),
  ('__future__', 'c:\\program files\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('glob', 'c:\\program files\\python\\lib\\glob.py', 'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('pywintypes',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('imp', 'c:\\program files\\python\\lib\\imp.py', 'PYMODULE'),
  ('pythoncom',
   'c:\\program files\\python\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('tempfile', 'c:\\program files\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python\\lib\\shutil.py', 'PYMODULE'),
  ('os', 'c:\\program files\\python\\lib\\os.py', 'PYMODULE'),
  ('pkg_resources',
   'c:\\program files\\python\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.six',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\six.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid', 'c:\\program files\\python\\lib\\uuid.py', 'PYMODULE'),
  ('netbios',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources.py31compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('mysql.connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\__init__.py',
   'PYMODULE'),
  ('mysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.pooling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\pooling.py',
   'PYMODULE'),
  ('mysql.connector.abstracts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\abstracts.py',
   'PYMODULE'),
  ('mysql.connector.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\utils.py',
   'PYMODULE'),
  ('mysql.connector.custom_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\custom_types.py',
   'PYMODULE'),
  ('mysql.connector.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\types.py',
   'PYMODULE'),
  ('mysql.connector.tls_ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\tls_ciphers.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\instrumentation.py',
   'PYMODULE'),
  ('mysql.connector.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\logger.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\constants.py',
   'PYMODULE'),
  ('mysql.connector.conversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\conversion.py',
   'PYMODULE'),
  ('mysql.connector.optionfiles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\optionfiles.py',
   'PYMODULE'),
  ('dns.resolver',
   'c:\\program files\\python\\lib\\site-packages\\dns\\resolver.py',
   'PYMODULE'),
  ('dns',
   'c:\\program files\\python\\lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns.version',
   'c:\\program files\\python\\lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('dns.tsig',
   'c:\\program files\\python\\lib\\site-packages\\dns\\tsig.py',
   'PYMODULE'),
  ('dns.reversename',
   'c:\\program files\\python\\lib\\site-packages\\dns\\reversename.py',
   'PYMODULE'),
  ('dns.rdatatype',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdatatype.py',
   'PYMODULE'),
  ('dns.enum',
   'c:\\program files\\python\\lib\\site-packages\\dns\\enum.py',
   'PYMODULE'),
  ('dns.rdataclass',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdataclass.py',
   'PYMODULE'),
  ('dns.rcode',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rcode.py',
   'PYMODULE'),
  ('dns.query',
   'c:\\program files\\python\\lib\\site-packages\\dns\\query.py',
   'PYMODULE'),
  ('requests',
   'c:\\program files\\python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\program files\\python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\program files\\python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\program files\\python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'c:\\program files\\python\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('json', 'c:\\program files\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\program files\\python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\program files\\python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\program files\\python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('idna',
   'c:\\program files\\python\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\program files\\python\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\program files\\python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\program files\\python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\program files\\python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\program files\\python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('ipaddress', 'c:\\program files\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.request',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\program files\\python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\program files\\python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\program files\\python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\program files\\python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\program files\\python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\program files\\python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\program files\\python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\program files\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\program files\\python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('fractions', 'c:\\program files\\python\\lib\\fractions.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('six', 'c:\\program files\\python\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('cryptography.hazmat._der',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\_der.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.cmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'c:\\program files\\python\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'c:\\program files\\python\\lib\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.interfaces',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\interfaces.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.encode_asn1',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\encode_asn1.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.x509.ocsp',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\ocsp.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ocsp',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ocsp.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x509',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x509.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('chardet',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('dns.serial',
   'c:\\program files\\python\\lib\\site-packages\\dns\\serial.py',
   'PYMODULE'),
  ('dns.name',
   'c:\\program files\\python\\lib\\site-packages\\dns\\name.py',
   'PYMODULE'),
  ('dns.wire',
   'c:\\program files\\python\\lib\\site-packages\\dns\\wire.py',
   'PYMODULE'),
  ('dns.message',
   'c:\\program files\\python\\lib\\site-packages\\dns\\message.py',
   'PYMODULE'),
  ('dns.update',
   'c:\\program files\\python\\lib\\site-packages\\dns\\update.py',
   'PYMODULE'),
  ('dns.rdataset',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdataset.py',
   'PYMODULE'),
  ('dns.set',
   'c:\\program files\\python\\lib\\site-packages\\dns\\set.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\OPT.py',
   'PYMODULE'),
  ('dns.renderer',
   'c:\\program files\\python\\lib\\site-packages\\dns\\renderer.py',
   'PYMODULE'),
  ('dns.rrset',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rrset.py',
   'PYMODULE'),
  ('dns.rdata',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdata.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\RRSIG.py',
   'PYMODULE'),
  ('dns.dnssec',
   'c:\\program files\\python\\lib\\site-packages\\dns\\dnssec.py',
   'PYMODULE'),
  ('dns.node',
   'c:\\program files\\python\\lib\\site-packages\\dns\\node.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\dsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\CH\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\CH\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\APL.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\AFSDB.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\KX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\RP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\PX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\util.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\IN\\NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\SOA.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\ANY\\DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   'c:\\program files\\python\\lib\\site-packages\\dns\\rdtypes\\dnskeybase.py',
   'PYMODULE'),
  ('dns.tokenizer',
   'c:\\program files\\python\\lib\\site-packages\\dns\\tokenizer.py',
   'PYMODULE'),
  ('dns.ttl',
   'c:\\program files\\python\\lib\\site-packages\\dns\\ttl.py',
   'PYMODULE'),
  ('dns.entropy',
   'c:\\program files\\python\\lib\\site-packages\\dns\\entropy.py',
   'PYMODULE'),
  ('dns.opcode',
   'c:\\program files\\python\\lib\\site-packages\\dns\\opcode.py',
   'PYMODULE'),
  ('dns.edns',
   'c:\\program files\\python\\lib\\site-packages\\dns\\edns.py',
   'PYMODULE'),
  ('dns.ipv6',
   'c:\\program files\\python\\lib\\site-packages\\dns\\ipv6.py',
   'PYMODULE'),
  ('dns.ipv4',
   'c:\\program files\\python\\lib\\site-packages\\dns\\ipv4.py',
   'PYMODULE'),
  ('dns.inet',
   'c:\\program files\\python\\lib\\site-packages\\dns\\inet.py',
   'PYMODULE'),
  ('dns.flags',
   'c:\\program files\\python\\lib\\site-packages\\dns\\flags.py',
   'PYMODULE'),
  ('dummy_threading',
   'c:\\program files\\python\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\program files\\python\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('dns.exception',
   'c:\\program files\\python\\lib\\site-packages\\dns\\exception.py',
   'PYMODULE'),
  ('mysql.connector.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\errors.py',
   'PYMODULE'),
  ('mysql.connector.locales',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\locales\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.dbapi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\dbapi.py',
   'PYMODULE'),
  ('mysql.connector.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\constants.py',
   'PYMODULE'),
  ('mysql.connector.charsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\charsets.py',
   'PYMODULE'),
  ('mysql.connector.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\connection.py',
   'PYMODULE'),
  ('mysql.connector.protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\protocol.py',
   'PYMODULE'),
  ('mysql.connector.plugins.caching_sha2_password',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\caching_sha2_password.py',
   'PYMODULE'),
  ('mysql.connector.plugins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\__init__.py',
   'PYMODULE'),
  ('mysql.connector.opentelemetry.context_propagation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\opentelemetry\\context_propagation.py',
   'PYMODULE'),
  ('mysql.connector.network',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\network.py',
   'PYMODULE'),
  ('mysql.connector.cursor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\cursor.py',
   'PYMODULE'),
  ('mysql.connector.authentication',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\authentication.py',
   'PYMODULE'),
  ('mysql.connector.connection_cext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\connection_cext.py',
   'PYMODULE'),
  ('mysql.connector.cursor_cext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\cursor_cext.py',
   'PYMODULE'),
  ('mysql.connector.errorcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\errorcode.py',
   'PYMODULE'),
  ('mysql.connector.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\version.py',
   'PYMODULE'),
  ('datetime', 'c:\\program files\\python\\lib\\datetime.py', 'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pytz',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('dataclasses', 'c:\\program files\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('markupsafe._constants',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\_constants.py',
   'PYMODULE'),
  ('markupsafe._compat',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\_compat.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.processors',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.pool.dbapi_proxy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\pool\\dbapi_proxy.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.interfaces',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.events',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.strategies',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.threadlocal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\threadlocal.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlite3',
   'c:\\program files\\python\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'c:\\program files\\python\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'c:\\program files\\python\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pysybase',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pysybase.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.zxJDBC',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\zxJDBC.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pypostgresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pypostgresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pygresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pygresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.oursql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\oursql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.gaerdbms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\gaerdbms.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.mxodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.mxodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.adodbapi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\adodbapi.py',
   'PYMODULE'),
  ('adodbapi',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\__init__.py',
   'PYMODULE'),
  ('adodbapi.adodbapi',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\adodbapi.py',
   'PYMODULE'),
  ('adodbapi.apibase',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\apibase.py',
   'PYMODULE'),
  ('adodbapi.process_connect_string',
   'c:\\program '
   'files\\python\\lib\\site-packages\\adodbapi\\process_connect_string.py',
   'PYMODULE'),
  ('adodbapi.is64bit',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\is64bit.py',
   'PYMODULE'),
  ('adodbapi.ado_consts',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\ado_consts.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.kinterbasdb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\kinterbasdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.fdb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\fdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\base.py',
   'PYMODULE'),
  ('MySQLdb',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.connections',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\connections.py',
   'PYMODULE'),
  ('MySQLdb.converters',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\converters.py',
   'PYMODULE'),
  ('MySQLdb.constants.FLAG',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\constants\\FLAG.py',
   'PYMODULE'),
  ('MySQLdb.constants.CLIENT',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\CLIENT.py',
   'PYMODULE'),
  ('MySQLdb._exceptions',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\_exceptions.py',
   'PYMODULE'),
  ('MySQLdb.cursors',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\cursors.py',
   'PYMODULE'),
  ('MySQLdb.times',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\times.py',
   'PYMODULE'),
  ('MySQLdb.constants.FIELD_TYPE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('MySQLdb.constants',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.release',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\release.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('lxml',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\program files\\python\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\program files\\python\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\program files\\python\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\program files\\python\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\program files\\python\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\program files\\python\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\program files\\python\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'c:\\program files\\python\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\fcompiler\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.config_compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\config_compiler.py',
   'PYMODULE'),
  ('numpy.distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.extension',
   'c:\\program files\\python\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.py31compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py31compat.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('distutils.cmd',
   'c:\\program files\\python\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.py33compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py33compat.py',
   'PYMODULE'),
  ('html.parser',
   'c:\\program files\\python\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase', 'c:\\program files\\python\\lib\\_markupbase.py', 'PYMODULE'),
  ('setuptools.ssl_support',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.py27compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py27compat.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('site',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\fake-modules\\site.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'c:\\program files\\python\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command',
   'c:\\program files\\python\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.install',
   'c:\\program files\\python\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'c:\\program files\\python\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'c:\\program files\\python\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'c:\\program files\\python\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.six',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_vendor\\six.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'c:\\program files\\python\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'c:\\program files\\python\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'c:\\program files\\python\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools.namespaces',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\namespaces.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools.extern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.command.develop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\develop.py',
   'PYMODULE'),
  ('setuptools.command.develop',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\develop.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_clib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('distutils.command.bdist_rpm',
   'c:\\program files\\python\\lib\\distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools.command.bdist_rpm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('numpy.distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools.command.install',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\install.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('distutils.command.install_headers',
   'c:\\program files\\python\\lib\\distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('numpy.distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('distutils.command.install_data',
   'c:\\program files\\python\\lib\\distutils\\command\\install_data.py',
   'PYMODULE'),
  ('numpy.distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('numpy.distutils.ccompiler_opt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\ccompiler_opt.py',
   'PYMODULE'),
  ('distutils.command.build_clib',
   'c:\\program files\\python\\lib\\distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('numpy.distutils.system_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\system_info.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_src',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_src.py',
   'PYMODULE'),
  ('numpy.distutils.conv_template',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\conv_template.py',
   'PYMODULE'),
  ('numpy.distutils.from_template',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\from_template.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput', 'c:\\program files\\python\\lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('distutils.command.build_py',
   'c:\\program files\\python\\lib\\distutils\\command\\build_py.py',
   'PYMODULE'),
  ('numpy.distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'c:\\program files\\python\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('numpy.distutils.command.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\config.py',
   'PYMODULE'),
  ('numpy.distutils.command.autodist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\command\\autodist.py',
   'PYMODULE'),
  ('numpy.distutils.mingw32ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\mingw32ccompiler.py',
   'PYMODULE'),
  ('numpy.distutils.lib2def',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\lib2def.py',
   'PYMODULE'),
  ('distutils.msvccompiler',
   'c:\\program files\\python\\lib\\distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'c:\\program files\\python\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.cygwinccompiler',
   'c:\\program files\\python\\lib\\distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.command.config',
   'c:\\program files\\python\\lib\\distutils\\command\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'c:\\program files\\python\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'c:\\program files\\python\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'c:\\program files\\python\\lib\\cgi.py', 'PYMODULE'),
  ('distutils.dist',
   'c:\\program files\\python\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'c:\\program files\\python\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('numpy.distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\core.py',
   'PYMODULE'),
  ('numpy.distutils.numpy_distribution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\numpy_distribution.py',
   'PYMODULE'),
  ('numpy.distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\extension.py',
   'PYMODULE'),
  ('numpy.distutils.fcompiler.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\fcompiler\\environment.py',
   'PYMODULE'),
  ('numpy.distutils._shell_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\_shell_utils.py',
   'PYMODULE'),
  ('pipes', 'c:\\program files\\python\\lib\\pipes.py', 'PYMODULE'),
  ('numpy.distutils.misc_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\misc_util.py',
   'PYMODULE'),
  ('numpy.distutils.npy_pkg_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\npy_pkg_config.py',
   'PYMODULE'),
  ('curses', 'c:\\program files\\python\\lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key',
   'c:\\program files\\python\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('numpy.distutils.exec_command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\exec_command.py',
   'PYMODULE'),
  ('numpy.distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.version',
   'c:\\program files\\python\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._psaix',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psaix.py',
   'PYMODULE'),
  ('psutil._pssunos',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pssunos.py',
   'PYMODULE'),
  ('psutil._psbsd',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psbsd.py',
   'PYMODULE'),
  ('psutil._psosx',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psosx.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._pslinux',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pslinux.py',
   'PYMODULE'),
  ('psutil._psposix',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_psposix.py',
   'PYMODULE'),
  ('psutil._compat',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\__config__.py',
   'PYMODULE'),
  ('numpy.distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\program files\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\program files\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\program files\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\program files\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\program files\\python\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('socket', 'c:\\program files\\python\\lib\\socket.py', 'PYMODULE'),
  ('scapy.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\all.py',
   'PYMODULE'),
  ('scapy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\__init__.py',
   'PYMODULE'),
  ('scapy.ansmachine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\ansmachine.py',
   'PYMODULE'),
  ('scapy.route6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\route6.py',
   'PYMODULE'),
  ('scapy.pton_ntop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\pton_ntop.py',
   'PYMODULE'),
  ('scapy.utils6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\utils6.py',
   'PYMODULE'),
  ('scapy.scapypipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\scapypipes.py',
   'PYMODULE'),
  ('scapy.layers.inet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\inet.py',
   'PYMODULE'),
  ('scapy.layers.inet6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\inet6.py',
   'PYMODULE'),
  ('scapy.layers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\__init__.py',
   'PYMODULE'),
  ('scapy.libs.matplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\matplot.py',
   'PYMODULE'),
  ('scapy.libs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.l2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\l2.py',
   'PYMODULE'),
  ('scapy.pipetool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\pipetool.py',
   'PYMODULE'),
  ('scapy.asn1.mib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\mib.py',
   'PYMODULE'),
  ('scapy.asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\__init__.py',
   'PYMODULE'),
  ('scapy.asn1.ber',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\ber.py',
   'PYMODULE'),
  ('scapy.asn1.asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1\\asn1.py',
   'PYMODULE'),
  ('scapy.layers.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\all.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_nrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_nrpc.py',
   'PYMODULE'),
  ('scapy.layers.dhcp6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dhcp6.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton_srv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\automaton_srv.py',
   'PYMODULE'),
  ('scapy.layers.ntlm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ntlm.py',
   'PYMODULE'),
  ('scapy.layers.gprs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\gprs.py',
   'PYMODULE'),
  ('scapy.layers.vrrp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\vrrp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_block',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_block.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.pkcs1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\pkcs1.py',
   'PYMODULE'),
  ('scapy.layers.dot11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dot11.py',
   'PYMODULE'),
  ('scapy.layers.dcerpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dcerpc.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.common_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\common_types.py',
   'PYMODULE'),
  ('scapy.contrib.rtps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\__init__.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.pid_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\pid_types.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.rtps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\rtps\\rtps.py',
   'PYMODULE'),
  ('scapy.contrib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tftp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tftp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.compression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\compression.py',
   'PYMODULE'),
  ('scapy.layers.bluetooth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\bluetooth.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_stream.py',
   'PYMODULE'),
  ('scapy.layers.dot15d4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dot15d4.py',
   'PYMODULE'),
  ('scapy.layers.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\http.py',
   'PYMODULE'),
  ('scapy.contrib.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\http2.py',
   'PYMODULE'),
  ('scapy.layers.tls.handshake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\handshake.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.dns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dns.py',
   'PYMODULE'),
  ('scapy.layers.rtp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\rtp.py',
   'PYMODULE'),
  ('scapy.layers.netflow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\netflow.py',
   'PYMODULE'),
  ('scapy.layers.llmnr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\llmnr.py',
   'PYMODULE'),
  ('scapy.layers.can',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\can.py',
   'PYMODULE'),
  ('scapy.layers.usb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\usb.py',
   'PYMODULE'),
  ('scapy.layers.tls.keyexchange_tls13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\keyexchange_tls13.py',
   'PYMODULE'),
  ('scapy.layers.lltd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\lltd.py',
   'PYMODULE'),
  ('scapy.layers.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ssh.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.groups',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\groups.py',
   'PYMODULE'),
  ('scapy.layers.bluetooth4LE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\bluetooth4LE.py',
   'PYMODULE'),
  ('scapy.contrib.ethercat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\contrib\\ethercat.py',
   'PYMODULE'),
  ('scapy.layers.snmp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\snmp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.hash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\hash.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\ciphers.py',
   'PYMODULE'),
  ('scapy.layers.tls.cert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\cert.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.msdrsr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\msdrsr.py',
   'PYMODULE'),
  ('scapy.layers.zigbee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\zigbee.py',
   'PYMODULE'),
  ('scapy.layers.eap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\eap.py',
   'PYMODULE'),
  ('scapy.layers.tls.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\extensions.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_wkst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_wkst.py',
   'PYMODULE'),
  ('scapy.layers.netbios',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\netbios.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.h_mac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\h_mac.py',
   'PYMODULE'),
  ('scapy.layers.mgcp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\mgcp.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\common.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.prf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\prf.py',
   'PYMODULE'),
  ('scapy.layers.sctp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\sctp.py',
   'PYMODULE'),
  ('scapy.layers.tls.handshake_sslv2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\handshake_sslv2.py',
   'PYMODULE'),
  ('scapy.layers.hsrp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\hsrp.py',
   'PYMODULE'),
  ('scapy.layers.radius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\radius.py',
   'PYMODULE'),
  ('scapy.layers.tls.record',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\record.py',
   'PYMODULE'),
  ('scapy.layers.kerberos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\kerberos.py',
   'PYMODULE'),
  ('scapy.libs.rfc3961',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\rfc3961.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('scapy.layers.ir',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ir.py',
   'PYMODULE'),
  ('scapy.layers.tls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.kx_algs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\kx_algs.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.md4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\md4.py',
   'PYMODULE'),
  ('scapy.layers.tls.keyexchange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\keyexchange.py',
   'PYMODULE'),
  ('scapy.layers.mobileip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\mobileip.py',
   'PYMODULE'),
  ('scapy.layers.rip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\rip.py',
   'PYMODULE'),
  ('scapy.layers.msrpce',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tls.record_tls13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\record_tls13.py',
   'PYMODULE'),
  ('scapy.layers.tls.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\all.py',
   'PYMODULE'),
  ('scapy.layers.l2tp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\l2tp.py',
   'PYMODULE'),
  ('scapy.layers.tls.tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\tools.py',
   'PYMODULE'),
  ('scapy.layers.gssapi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\gssapi.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\automaton.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_samr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_samr.py',
   'PYMODULE'),
  ('scapy.layers.dhcp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\dhcp.py',
   'PYMODULE'),
  ('scapy.layers.tls.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\session.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_srvs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_srvs.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton_cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\automaton_cli.py',
   'PYMODULE'),
  ('scapy.layers.smbserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smbserver.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.suites',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\suites.py',
   'PYMODULE'),
  ('scapy.layers.tls.basefields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\basefields.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_aead.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.ept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\ept.py',
   'PYMODULE'),
  ('scapy.layers.spnego',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\spnego.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ept.py',
   'PYMODULE'),
  ('scapy.layers.clns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\clns.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.mspac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\mspac.py',
   'PYMODULE'),
  ('scapy.layers.sixlowpan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\sixlowpan.py',
   'PYMODULE'),
  ('scapy.layers.ipsec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ipsec.py',
   'PYMODULE'),
  ('scapy.layers.pptp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\pptp.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_dcom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_dcom.py',
   'PYMODULE'),
  ('scapy.layers.tuntap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tuntap.py',
   'PYMODULE'),
  ('scapy.layers.isakmp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\isakmp.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\all.py',
   'PYMODULE'),
  ('scapy.layers.skinny',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\skinny.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.rpcserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\rpcserver.py',
   'PYMODULE'),
  ('scapy.layers.smb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smb.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.hkdf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\hkdf.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.hkdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\hkdf.py',
   'PYMODULE'),
  ('scapy.layers.vxlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\vxlan.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.rpcclient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\rpcclient.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.raw.ms_drsr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\raw\\ms_drsr.py',
   'PYMODULE'),
  ('scapy.layers.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\x509.py',
   'PYMODULE'),
  ('scapy.layers.ldap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ldap.py',
   'PYMODULE'),
  ('scapy.layers.smbclient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smbclient.py',
   'PYMODULE'),
  ('scapy.layers.tls.record_sslv2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\record_sslv2.py',
   'PYMODULE'),
  ('scapy.layers.ppp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ppp.py',
   'PYMODULE'),
  ('scapy.layers.pflog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\pflog.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.msnrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\msnrpc.py',
   'PYMODULE'),
  ('scapy.layers.msrpce.msdcom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\msrpce\\msdcom.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.all',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\tls\\crypto\\all.py',
   'PYMODULE'),
  ('scapy.layers.ppi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ppi.py',
   'PYMODULE'),
  ('scapy.layers.smb2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\smb2.py',
   'PYMODULE'),
  ('scapy.layers.ntp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\layers\\ntp.py',
   'PYMODULE'),
  ('scapy.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\compat.py',
   'PYMODULE'),
  ('scapy.consts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\consts.py',
   'PYMODULE'),
  ('scapy.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\main.py',
   'PYMODULE'),
  ('rlcompleter', 'c:\\program files\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('scapy.autorun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\autorun.py',
   'PYMODULE'),
  ('scapy.automaton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\automaton.py',
   'PYMODULE'),
  ('scapy.as_resolvers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\as_resolvers.py',
   'PYMODULE'),
  ('scapy.volatile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\volatile.py',
   'PYMODULE'),
  ('scapy.supersocket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\supersocket.py',
   'PYMODULE'),
  ('scapy.arch.linux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\linux\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.linux.rtnetlink',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\linux\\rtnetlink.py',
   'PYMODULE'),
  ('scapy.libs.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\structures.py',
   'PYMODULE'),
  ('scapy.arch.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\common.py',
   'PYMODULE'),
  ('scapy.libs.winpcapy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\winpcapy.py',
   'PYMODULE'),
  ('scapy.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\sessions.py',
   'PYMODULE'),
  ('scapy.sendrecv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\sendrecv.py',
   'PYMODULE'),
  ('scapy.route',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\route.py',
   'PYMODULE'),
  ('scapy.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\utils.py',
   'PYMODULE'),
  ('scapy.arch.windows.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\windows\\structures.py',
   'PYMODULE'),
  ('scapy.arch.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\windows\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.libpcap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\libpcap.py',
   'PYMODULE'),
  ('scapy.arch.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\unix.py',
   'PYMODULE'),
  ('scapy.asn1packet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1packet.py',
   'PYMODULE'),
  ('scapy.asn1fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\asn1fields.py',
   'PYMODULE'),
  ('scapy.packet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\packet.py',
   'PYMODULE'),
  ('scapy.libs.test_pyx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\test_pyx.py',
   'PYMODULE'),
  ('scapy.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\fields.py',
   'PYMODULE'),
  ('scapy.plist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\plist.py',
   'PYMODULE'),
  ('scapy.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\interfaces.py',
   'PYMODULE'),
  ('scapy.arch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.windows.native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\windows\\native.py',
   'PYMODULE'),
  ('scapy.arch.solaris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\solaris.py',
   'PYMODULE'),
  ('scapy.arch.bpf.supersocket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\supersocket.py',
   'PYMODULE'),
  ('scapy.arch.bpf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.bpf.consts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\consts.py',
   'PYMODULE'),
  ('scapy.arch.bpf.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\core.py',
   'PYMODULE'),
  ('scapy.arch.bpf.pfroute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\arch\\bpf\\pfroute.py',
   'PYMODULE'),
  ('scapy.libs.extcap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\extcap.py',
   'PYMODULE'),
  ('scapy.themes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\themes.py',
   'PYMODULE'),
  ('scapy.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\error.py',
   'PYMODULE'),
  ('colorama',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\program files\\python\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('scapy.data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\data.py',
   'PYMODULE'),
  ('scapy.libs.manuf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\manuf.py',
   'PYMODULE'),
  ('scapy.libs.ethertypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\libs\\ethertypes.py',
   'PYMODULE'),
  ('scapy.dadict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\dadict.py',
   'PYMODULE'),
  ('scapy.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\config.py',
   'PYMODULE'),
  ('scapy.modules.nmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\modules\\nmap.py',
   'PYMODULE'),
  ('scapy.modules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\modules\\__init__.py',
   'PYMODULE'),
  ('scapy.base_classes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\scapy\\base_classes.py',
   'PYMODULE')],
 [('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('python38.dll', 'c:\\program files\\python\\python38.dll', 'BINARY'),
  ('VCRUNTIME140.dll', 'c:\\program files\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('pywintypes38.dll',
   'c:\\program files\\python\\lib\\site-packages\\win32\\pywintypes38.dll',
   'BINARY'),
  ('pythoncom38.dll',
   'c:\\program '
   'files\\python\\Lib\\site-packages\\pywin32_system32\\pythoncom38.dll',
   'BINARY'),
  ('libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('win32trace',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32ui',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('unicodedata',
   'c:\\program files\\python\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_hashlib', 'c:\\program files\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma', 'c:\\program files\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2', 'c:\\program files\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select', 'c:\\program files\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_overlapped',
   'c:\\program files\\python\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl', 'c:\\program files\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_asyncio', 'c:\\program files\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_ctypes', 'c:\\program files\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing',
   'c:\\program files\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_queue', 'c:\\program files\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('pyexpat', 'c:\\program files\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal', 'c:\\program files\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_testcapi', 'c:\\program files\\python\\DLLs\\_testcapi.pyd', 'EXTENSION'),
  ('_tkinter', 'c:\\program files\\python\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('_elementtree',
   'c:\\program files\\python\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32evtlog',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('_win32sysloader',
   'c:\\program files\\python\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32api',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com.shell.shell',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32wnet',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('_cffi_backend',
   'c:\\program '
   'files\\python\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography.hazmat.bindings._openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt._bcrypt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\bcrypt\\_bcrypt.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_mysql_connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\_mysql_connector.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.aggregations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.np_datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timestamps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.conversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.ccalendar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.algos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timezones',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.join',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\join.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.vectorized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.parsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.tzconversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\json.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops_dispatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.internals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.index',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\index.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.strptime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.nattype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.writers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reduction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.offsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe._speedups',
   'c:\\program '
   'files\\python\\lib\\site-packages\\markupsafe\\_speedups.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cprocessors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cprocessors.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cresultproxy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cresultproxy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cutils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cutils.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3', 'c:\\program files\\python\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('MySQLdb._mysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\_mysql.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.etree',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\etree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml._elementpath',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\_elementpath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('psutil._psutil_windows',
   'c:\\program '
   'files\\python\\lib\\site-packages\\psutil\\_psutil_windows.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32pdh',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas.io.sas._sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas.io.sas._byteswap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashtable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_socket', 'c:\\program files\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('mfc140u.dll',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\program files\\python\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\program files\\python\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll', 'c:\\program files\\python\\DLLs\\libffi-7.dll', 'BINARY'),
  ('tcl86t.dll', 'c:\\program files\\python\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'c:\\program files\\python\\DLLs\\tk86t.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('sqlite3.dll', 'c:\\program files\\python\\DLLs\\sqlite3.dll', 'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\develop\\python\\test\\build\\scan\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tk\\megawidget.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\safe.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\entry.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tk\\tearoff.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tk\\text.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\parray.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tk\\images\\README',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tk\\optMenu.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tk\\tclIndex', 'c:\\program files\\python\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\history.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('Include\\pyconfig.h',
   'c:\\program files\\python\\Include\\pyconfig.h',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\init.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\METADATA',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\METADATA',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\INSTALLER',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\INSTALLER',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\tclIndex', 'c:\\program files\\python\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\clock.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\AUTHORS.rst',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\AUTHORS.rst',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tk\\clrpick.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\posixrules',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\posixrules',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tk\\obsolete.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tk\\console.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('tcl\\tm.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tk\\iconlist.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\top_level.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tk\\images\\logo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\word.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('lib2to3\\tests\\data\\README',
   'c:\\program files\\python\\lib\\lib2to3\\tests\\data\\README',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.PSF',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.PSF',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tk\\palette.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tk\\icons.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tk\\license.terms',
   'c:\\program files\\python\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tk\\scale.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tk\\comdlg.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tcl\\auto.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.APACHE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tcl\\package.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tk\\choosedir.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tk\\bgerror.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.BSD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.BSD',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\dialog.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tk\\focus.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('lib2to3\\Grammar.txt',
   'c:\\program files\\python\\lib\\lib2to3\\Grammar.txt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tk\\listbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tk\\tk.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\RECORD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\RECORD',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('lib2to3\\PatternGrammar.txt',
   'c:\\program files\\python\\lib\\lib2to3\\PatternGrammar.txt',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tk\\unsupported.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tk\\menu.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\WHEEL',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\WHEEL',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tk\\safetk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA')],
 [])
