#!/bin/bash

# 定义需要删除的目录和文件
PYTHON_BINARIES=(
    "/usr/bin/python"
    "/usr/bin/python2"
    "/usr/bin/python3"
    "/usr/local/bin/python"
    "/usr/local/bin/python2"
    "/usr/local/bin/python3"
    "/root/.pyenv"
)

PYTHON_DIRECTORIES=(
    "/usr/lib/python*"
    "/usr/local/lib/python*"
    "/usr/local/include/python*"
    "/usr/local/lib64/python*"
    "/usr/share/python*"
    "/usr/lib64/python*"
    "/home/<USER>/.local/lib/python*"
    "/home/<USER>/.pyenv"
)

ECHO_BLUE="\e[34m"
ECHO_GREEN="\e[32m"
ECHO_YELLOW="\e[33m"
ECHO_RED="\e[31m"
ECHO_RESET="\e[0m"

# 检查用户权限
if [[ "$(id -u)" -ne 0 ]]; then
    echo -e "${ECHO_RED}[ERROR]${ECHO_RESET} 请使用 root 用户运行此脚本！"
    exit 1
fi

# 确认操作
echo -e "${ECHO_YELLOW}[WARNING]${ECHO_RESET} 即将从系统中彻底移除所有 Python 版本及相关文件！"
read -p "确定要继续吗？(yes/no): " CONFIRM
if [[ "$CONFIRM" != "yes" ]]; then
    echo -e "${ECHO_BLUE}[INFO]${ECHO_RESET} 操作已取消。"
    exit 0
fi

# 删除 Python 二进制文件
echo -e "${ECHO_BLUE}[INFO]${ECHO_RESET} 正在删除 Python 二进制文件..."
for binary in "${PYTHON_BINARIES[@]}"; do
    if [[ -e "$binary" ]]; then
        echo -e "${ECHO_GREEN}[REMOVING]${ECHO_RESET} $binary"
        rm -f "$binary"
    else
        echo -e "${ECHO_YELLOW}[SKIP]${ECHO_RESET} $binary 不存在，跳过。"
    fi
done

# 删除 Python 相关目录
echo -e "${ECHO_BLUE}[INFO]${ECHO_RESET} 正在删除 Python 相关目录..."
for directory in "${PYTHON_DIRECTORIES[@]}"; do
    echo -e "${ECHO_GREEN}[REMOVING]${ECHO_RESET} $directory"
    rm -rf $directory
done

# 移除符号链接
echo -e "${ECHO_BLUE}[INFO]${ECHO_RESET} 正在检查并移除符号链接..."
if [[ -L "/usr/bin/python" ]]; then
    echo -e "${ECHO_GREEN}[REMOVING]${ECHO_RESET} 符号链接 /usr/bin/python"
    rm -f /usr/bin/python
fi
if [[ -L "/usr/bin/python2" ]]; then
    echo -e "${ECHO_GREEN}[REMOVING]${ECHO_RESET} 符号链接 /usr/bin/python2"
    rm -f /usr/bin/python2
fi
if [[ -L "/usr/bin/python3" ]]; then
    echo -e "${ECHO_GREEN}[REMOVING]${ECHO_RESET} 符号链接 /usr/bin/python3"
    rm -f /usr/bin/python3
fi

# 检查是否成功清理
echo -e "${ECHO_BLUE}[INFO]${ECHO_RESET} 检查 Python 是否仍然存在..."
which python && echo -e "${ECHO_RED}[ERROR]${ECHO_RESET} Python 未完全删除，请手动检查！" || echo -e "${ECHO_GREEN}[CLEANED]${ECHO_RESET} Python 已被完全移除。"
which python2 && echo -e "${ECHO_RED}[ERROR]${ECHO_RESET} Python 2 未完全删除，请手动检查！" || echo -e "${ECHO_GREEN}[CLEANED]${ECHO_RESET} Python 2 已被完全移除。"
which python3 && echo -e "${ECHO_RED}[ERROR]${ECHO_RESET} Python 3 未完全删除，请手动检查！" || echo -e "${ECHO_GREEN}[CLEANED]${ECHO_RESET} Python 3 已被完全移除。"

echo -e "${ECHO_BLUE}[INFO]${ECHO_RESET} 清理完成。"
