('c:\\program files\\python\\tcl\\tk8.6',
 'tk',
 ['demos', '*.lib', 'tkConfig.sh'],
 'DATA',
 [('tk\\bgerror.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tk\\choosedir.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tk\\clrpick.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tk\\comdlg.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tk\\console.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tk\\dialog.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tk\\entry.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tk\\focus.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tk\\fontchooser.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tk\\iconlist.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tk\\icons.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tk\\license.terms',
   'c:\\program files\\python\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tk\\listbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tk\\megawidget.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tk\\menu.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tk\\mkpsenc.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tk\\msgbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tk\\obsolete.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\optMenu.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tk\\palette.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tk\\safetk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tk\\scale.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tk\\scrlbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tk\\tclIndex', 'c:\\program files\\python\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tk\\tearoff.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tk\\text.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tk\\tk.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tk\\tkfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tk\\unsupported.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\images\\logo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\images\\README',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA')])
