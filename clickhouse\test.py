import re


def extract_sql(result: str) -> str:
    """
    从给定的字符串中提取被$$$包裹的SQL语句。
    :param result: 包含SQL语句的字符串
    :return: 提取出的SQL语句
    """
    # 使用正则表达式匹配被$$$包裹的SQL语句
    sql_pattern = r'\$\$\$(.*?)\$\$\$'
    match = re.search(sql_pattern, result, re.DOTALL)
    print (match)

    if match:
        # 去除SQL语句前后的空白字符
        sql_statement = match.group(1).strip()
        return sql_statement
    else:
        return "未找到SQL语句"


def main(result: str) -> dict:
    """
    主函数，用于提取SQL语句并返回结果。
    :param result: 包含SQL语句的字符串
    :return: 包含提取结果的字典
    """
    sql_statement = extract_sql(result)

    return {
        "result": sql_statement,
    }


# 示例使用
if __name__ == "__main__":
    result_text = """
    <think>
    好吧，我现在需要解决这个问题：从表arp中获取所有类型为‘笔记本’的记录。首先，我得理解用户提供的表结构。
    表arp有一个字段叫做type，数据类型是varchar(255)，可以存储字符串，比如‘笔记本’。我的任务就是筛选出type等于‘笔记本’的行，然后提取相关的数据。
    接下来，我要考虑如何构造这个SQL查询。因为用户要求只使用他们提到的表和字段，所以我只能从arp表中获取信息，不能引用其他表或字段。
    然后，确保SQL兼容SQLServer2014。这意味着我需要注意一些语法细节，比如字符串的引号是否正确使用，或者是否有任何特定的排除项。幸好，在这个案例中并不复杂，所以应该没问题。
    用户还要求只用简体中文，不要繁体中文，这点已经满足，因为type字段中的值是‘笔记本’，属于简体中文。
    接下来，我需要确保只输出一个完整的SQL语句，没有注释或其他多余内容。所以，我得写出一个简单的SELECT语句，选择所需的字段，然后加上WHERE子句来筛选type等于目标值。
    最后，用$$$包裹整个SQL语句，这样用户就能方便地复制和使用这个查询了。
    总结一下，我的思考过程包括：确定表结构、识别需要筛选的字段、构造正确的WHERE子句，并确保符合所有要求，最终生成一个准确的SQL语句。
    </think>
    $$$
    SELECT hostname, mac, type, user FROM arp WHERE type = '笔记本'
    $$$
    """

    output = main(result_text)
    print(output)
