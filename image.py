import os
from PIL import Image, ImageOps

# 定义目标文件夹路径
folder_path = r'D:\Backup\Desktop\配载分舱图有问题图片'

# 定义目标尺寸
target_width, target_height = 600, 800

# 定义最小 DPI
min_dpi = 72

# 遍历文件夹中的所有文件
for filename in os.listdir(folder_path):
    # 检查文件是否为.jpg文件
    if filename.lower().endswith('.jpg'):
        # 构建完整的文件路径
        file_path = os.path.join(folder_path, filename)

        # 打开图片
        img = Image.open(file_path)

        # 处理图片的 EXIF 方向信息，确保方向正确
        img = ImageOps.exif_transpose(img)

        # 获取图片的 DPI 信息
        dpi = img.info.get('dpi', (72, 72))  # 如果 DPI 信息不存在，默认为 72
        if dpi[0] < min_dpi or dpi[1] < min_dpi:
            dpi = (min_dpi, min_dpi)

        # 获取原图尺寸
        width, height = img.size

        # 判断图片的宽高方向
        if width > height:
            # 图片是横向的，按宽度缩放
            ratio = target_width / width
            new_width = target_width
            new_height = int(height * ratio)
        else:
            # 图片是竖向的，按高度缩放
            ratio = target_height / height
            new_height = target_height
            new_width = int(width * ratio)

        # 缩放图片，使用 LANCZOS 算法以保持高质量
        img = img.resize((new_width, new_height), Image.LANCZOS)

        # 创建新的空白图片，背景为白色
        new_img = Image.new('RGB', (target_width, target_height), (255, 255, 255))

        # 计算粘贴位置，使图片居中
        paste_position = ((target_width - new_width) // 2, (target_height - new_height) // 2)

        # 将缩放后的图片粘贴到新图片上
        new_img.paste(img, paste_position)

        # 保存处理后的图片，覆盖原文件，并保持 DPI
        new_img.save(file_path, dpi=dpi)

print("所有图片处理完成！")
