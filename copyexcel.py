import os
import shutil
import openpyxl

# 配置路径
excel_path = 'D:\Backup\excel.xlsx'  # Excel文件路径
folder_a_path = 'D:\Backup\文件夹技贸页'       # 源文件夹路径
folder_b_path = 'D:\Backup\文件夹上传合同页'       # 目标文件夹路径

# 确保目标文件夹存在
os.makedirs(folder_b_path, exist_ok=True)

# 读取Excel文件
wb = openpyxl.load_workbook(excel_path)
ws = wb.active  # 获取活动工作表

# 收集A列中的所有合同号（假设从第1行开始）
contract_numbers = []
for row in ws.iter_rows(min_col=1, max_col=1, values_only=True):
    if row[0] is not None:  # 忽略空单元格
        contract_numbers.append(str(row[0]).strip())  # 转换为字符串并去除空格

# 扫描文件夹A并复制匹配的文件
for filename in os.listdir(folder_a_path):
    for contract in contract_numbers:
        if contract in filename:  # 如果合同号是文件名的一部分
            src_path = os.path.join(folder_a_path, filename)
            dst_path = os.path.join(folder_b_path, filename)
            shutil.copy2(src_path, dst_path)  # 复制文件并保留元数据
            print(f"已复制: {filename}")
            break  # 找到一个匹配就跳出内层循环

print("文件复制完成！")
