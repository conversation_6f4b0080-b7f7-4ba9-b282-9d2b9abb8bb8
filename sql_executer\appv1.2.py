from flask import Flask, request, jsonify
import mysql.connector
from mysql.connector import Error
from decimal import Decimal
import logging
import os

app = Flask(__name__)
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'run.log')),  # 日志保存到 run.log
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
# 数据库连接配置
DB_CONFIGS = {
    'db_bi': {
        'host': '************',
        'user': 'llm_readonly',
        'port': 3306,
        'password': 'LLM%2025Rdv1ew',
        'database': 'bi_hna_cost'
    },
    'db_qa': {
        'host': '**************',
        'user': 'data_ai_read',
        'port': 3307,
        'password': 'qftB!zck3c1',
        'database': 'data_qa'
    },
    'db_wiki': {
        'host': '************',
        'user': 'data_ai_read',
        'port': 3306,
        'password': 'qftB!zck3c1',
        'database': 'data_wiki'
    }
}


# 连接到MySQL数据库
def get_db_connection(db):
    try:
        config = DB_CONFIGS.get(db)
        if not config:
            raise ValueError(f"Database configuration for '{db}' not found")
        conn = mysql.connector.connect(**config)
        return conn
    except Exception as e:
        logging.error(f"Error connecting to database {db}: {e}")
        return None


# 将结果中的 Decimal 类型转换为 float
def convert_decimal(data):
    if isinstance(data, list):
        return [convert_decimal(item) for item in data]
    elif isinstance(data, dict):
        return {key: convert_decimal(value) for key, value in data.items()}
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data


# 检查SQL语句是否有效
def validate_sql(sql_query):
    sql_query = sql_query.strip().upper()
    if not sql_query.startswith(('SELECT', 'INSERT', 'UPDATE', 'DELETE')):
        raise ValueError("Invalid SQL statement. Only SELECT, INSERT, UPDATE, DELETE are allowed.")
    return sql_query


# 执行SQL查询并返回结果
def execute_query(db, sql_query, is_echart=False):
    conn = get_db_connection(db)
    if conn is None:
        logging.error(f"Unable to connect to the database: {db}")
        return None, f"Unable to connect to the database: {db}"
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(sql_query)
        if sql_query.startswith('SELECT'):
            rows = cursor.fetchall()
            converted_rows = convert_decimal(rows)
            if is_echart:
                column_names = list(converted_rows[0].keys()) if converted_rows else []
                if len(column_names) < 2:
                    logging.error("Query result must have at least two columns")
                    return {"xAxis": "", "yAxis": ""}, None
                x_axis_name = column_names[0]
                y_axis_name = column_names[1]
                xAxis = ";".join([str(row[x_axis_name]) for row in converted_rows])
                yAxis = ";".join([str(row[y_axis_name]) for row in converted_rows])
                return {"xAxis": xAxis, "yAxis": yAxis}, None
            else:
                return converted_rows, None
        else:
            conn.commit()
            logging.info(f"Query executed successfully on {db}: {sql_query}")
            return {"message": "Query executed successfully", "changes": cursor.rowcount}, None
    except Error as e:
        logging.error(f"Database error on {db}: {e}")
        return None, str(e)
    except KeyError as e:
        logging.error(f"Invalid column name on {db}: {e}")
        return None, f"Invalid column name: {str(e)}"
    finally:
        cursor.close()
        conn.close()


@app.route('/execute', methods=['POST'])
def execute_sql():
    # 记录入参
    logging.info(f"Received request data: {request.get_json()}")
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    if data is None or 'sql' not in data:
        logging.warning("Missing 'sql' parameter")
        return jsonify({"error": "Missing 'sql' parameter"}), 400
    # 处理 db 参数
    db = data.get('db', 'db_bi')  # 如果没有传入 'db'，则默认为 'db_bi'
    if not db:  # 如果传入的 db 参数为空
        logging.error("Invalid 'db' parameter: empty value")
        return jsonify({"error": "Invalid 'db' parameter: empty value"}), 400
    if db not in DB_CONFIGS:  # 如果 db 参数不在 DB_CONFIGS 中
        logging.error(f"Invalid 'db' parameter: '{db}' not found in DB_CONFIGS")
        return jsonify({"error": f"Invalid 'db' parameter: '{db}' not found in DB_CONFIGS"}), 400
    sql_query = data['sql']
    is_echart = data.get('isEchart', True)
    try:
        sql_query = validate_sql(sql_query)
    except Exception as e:
        logging.error(f"Invalid SQL statement: {e}")
        return jsonify({"error": str(e)}), 400
    result, error = execute_query(db, sql_query, is_echart)
    if error:
        return jsonify({"error": error}), 400
    return jsonify(result)


@app.route('/getPrompt', methods=['POST'])
def get_prompt():
    # 记录入参
    logging.info(f"Received request data: {request.get_json()}")
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    if data is None or 'sql' not in data:
        logging.warning("Missing 'sql' parameter")
        return jsonify({"error": "Missing 'sql' parameter"}), 400
    sql_query = data['sql']
    db = data.get('db', 'db_wiki')  # 如果没有传入 'db'，则默认为 'db_wiki'
    try:
        sql_query = validate_sql(sql_query)
    except Exception as e:
        logging.error(f"Invalid SQL statement: {e}")
        return jsonify({"error": str(e)}), 400
    result, error = execute_query(db, sql_query)
    if error:
        return jsonify({"error": error}), 400
    return jsonify(result)


@app.route('/getPromptByName', methods=['POST'])
def get_prompt_by_name():
    # 记录入参
    logging.info(f"Received request data: {request.get_json()}")

    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400

    data = request.get_json()
    if data is None or 'keyword' not in data:
        logging.warning("Missing 'keyword' parameter")
        return jsonify({"error": "Missing 'keyword' parameter"}), 400

    keyword = data['keyword']
    # 拼接 SQL 语句
    sql_query = f'SELECT sql_prompt FROM idx_index_prompt WHERE index_name LIKE "%{keyword}%"'

    try:
        # 验证 SQL 语句
        sql_query = validate_sql(sql_query)
    except Exception as e:
        logging.error(f"Invalid SQL statement: {e}")
        return jsonify({"error": str(e)}), 400

    # 执行查询
    result, error = execute_query('db_wiki', sql_query)
    if error:
        return jsonify({"error": error}), 400

    return jsonify(result)


@app.route('/authentication', methods=['POST'])
def authentication():
    # 记录入参
    logging.info(f"Received authentication request data: {request.get_json()}")
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    logging.info(f"Received authentication request: {data}")
    return jsonify(data)


if __name__ == '__main__':
    logging.info("Starting Flask application")
    app.run(host='0.0.0.0', port=5000, debug=True)
