# RTSP流转发器

这是一个简单的Python脚本，用于将RTSP流转发为HTTP流，以便在浏览器中实时播放。

## 功能特性

1. 将RTSP流转发为HTTP流
2. 提供Web界面实时播放
3. 不使用缓存，实时播放
4. 自动重连RTSP流

## 依赖安装

在运行脚本之前，建议使用Python虚拟环境并安装以下依赖：

### 创建和激活虚拟环境

```bash
# 创建虚拟环境
python -m venv .venv

# Windows激活虚拟环境
.venv\Scripts\activate

# Linux/MacOS激活虚拟环境
source .venv/bin/activate
```

### 安装依赖包

```bash
# 使用虚拟环境中的pip安装
pip install opencv-python flask
```

### 验证依赖安装

安装完成后，建议验证依赖是否正确安装：

```bash
# 创建测试脚本
python -c "import cv2; import flask; print('OpenCV version:', cv2.__version__); print('Flask version:', flask.__version__)"
```

如果输出了版本号，说明依赖安装成功。

### 常见问题：pip缺失

如果遇到`No module named pip`错误，可以使用以下命令重新安装pip：

```bash
# 重新安装pip
python -m ensurepip --upgrade
```

### OpenCV安装说明

OpenCV是用于处理视频流的核心库。在某些系统上，可能需要额外的步骤来安装OpenCV：

**Windows:**
```bash
pip install opencv-python
```

**Linux (Ubuntu/Debian):**
```bash
# 安装系统依赖
sudo apt-get update
sudo apt-get install python3-opencv

# 或者使用pip安装
pip3 install opencv-python
```

**macOS:**
```bash
# 使用Homebrew安装
brew install opencv

# 或者使用pip安装
pip3 install opencv-python
```

### Flask安装

Flask是用于创建Web服务器的轻量级框架：

```bash
pip install flask
```

## 使用方法

1. 克隆或下载此项目
2. 安装依赖（如上所述）
3. 运行脚本（确保已激活虚拟环境）：

```bash
# 使用默认RTSP地址
python rtsp_stream_viewer.py

# 或指定RTSP地址
python rtsp_stream_viewer.py --rtsp-url "rtsp://username:password@ip:port/path"

# 指定主机和端口
python rtsp_stream_viewer.py --host 127.0.0.1 --port 8080
```

## 访问流媒体

运行脚本后，可以通过以下地址在浏览器中访问视频流：

```
http://服务器IP:5000
```

例如，在本地运行时访问：

```
http://localhost:5000
```

视频流将显示在页面的图像标签中。

## 配置参数

- `--rtsp-url`: RTSP流地址（默认：rtsp://admin:123456@************:554/video1）
- `--host`: 服务器主机地址（默认：0.0.0.0）
- `--port`: 服务器端口号（默认：5000）

## 工作原理

1. 脚本使用OpenCV连接到RTSP流
2. 在单独的线程中读取视频帧并放入队列
3. Flask服务器提供Web界面
4. 通过`/video`端点将视频帧以MJPEG格式流式传输

## 经验总结

1. 始终使用虚拟环境来隔离项目依赖，避免与系统Python环境冲突
2. 确保使用虚拟环境中的Python解释器和pip
3. 如果遇到依赖导入问题，先验证依赖是否正确安装
4. 对于Windows用户，确保命令行以管理员权限运行（如果需要）
5. 如果RTSP流连接不稳定，检查网络连接和RTSP地址是否正确