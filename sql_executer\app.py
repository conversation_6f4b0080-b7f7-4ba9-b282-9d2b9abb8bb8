from flask import Flask, request, jsonify
import mysql.connector
from mysql.connector import Error
from decimal import Decimal
import logging
from logging.handlers import RotatingFileHandler
import os
import json

app = Flask(__name__)
# 配置 Flask，确保 jsonify 返回 UTF-8 编码的 JSON 数据
app.config['JSON_AS_ASCII'] = False  # 禁用 ASCII 转义
app.config['JSONIFY_MIMETYPE'] = 'application/json; charset=utf-8'  # 显式设置字符集


# 配置日志
def setup_logger():
    # 创建日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    # 创建文件处理器，确保日志文件使用 UTF-8 编码
    file_handler = RotatingFileHandler(
        'app.log', maxBytes=1024 * 1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    # 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    # 移除 Flask 默认的带颜色的日志处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    # 添加处理器到记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)


# 初始化日志配置
setup_logger()


# 数据库连接配置
DB_CONFIGS = {
    'db_bi': {
        'host': '************',
        'user': 'llm_readonly',
        'port': 3306,
        'password': 'LLM%2025Rdv1ew',
        'database': 'bi_hna_cost'
    },
    'db_qa': {
        'host': '**************',
        'user': 'data_ai_read',
        'port': 3307,
        'password': 'qftB!zck3c1',
        'database': 'data_qa'
    },
    'db_wiki': {
        'host': '************',
        'user': 'data_ai_read',
        'port': 3306,
        'password': 'qftB!zck3c1',
        'database': 'data_wiki'
    }
}


# 连接到MySQL数据库
def get_db_connection(db):
    try:
        config = DB_CONFIGS.get(db)
        if not config:
            raise ValueError(f"Database configuration for '{db}' not found")
        conn = mysql.connector.connect(**config)
        return conn
    except Exception as e:
        logging.error(f"Error connecting to database {db}: {e}")
        return None


# 将结果中的 Decimal 类型转换为 float
def convert_decimal(data):
    if isinstance(data, list):
        return [convert_decimal(item) for item in data]
    elif isinstance(data, dict):
        return {key: convert_decimal(value) for key, value in data.items()}
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data


# 检查SQL语句是否有效
def validate_sql(sql_query):
    sql_query = sql_query.strip().upper()
    if not sql_query.startswith(('SELECT', 'INSERT', 'UPDATE', 'DELETE')):
        raise ValueError("Invalid SQL statement. Only SELECT, INSERT, UPDATE, DELETE are allowed.")
    return sql_query


# 执行SQL查询并返回结果
def execute_query_bak(db, sql_query, is_echart=False):
    conn = get_db_connection(db)
    if conn is None:
        logging.error(f"Unable to connect to the database: {db}")
        return None, f"Unable to connect to the database: {db}"
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(sql_query)
        if sql_query.startswith('SELECT'):
            rows = cursor.fetchall()
            converted_rows = convert_decimal(rows)
            if is_echart:
                column_names = list(converted_rows[0].keys()) if converted_rows else []
                if len(column_names) < 2:
                    logging.error("Query result must have at least two columns")
                    return {"xAxis": "", "yAxis": ""}, None
                x_axis_name = column_names[0]
                y_axis_name = column_names[1]
                xAxis = ";".join([str(row[x_axis_name]) for row in converted_rows])
                yAxis = ";".join([str(row[y_axis_name]) for row in converted_rows])
                return {"xAxis": xAxis, "yAxis": yAxis}, None
            else:
                return converted_rows, None
        else:
            conn.commit()
            logging.info(f"Query executed successfully on {db}: {sql_query}")
            return {"message": "Query executed successfully", "changes": cursor.rowcount}, None
    except Error as e:
        logging.error(f"Database error on {db}: {e}")
        return None, str(e)
    except KeyError as e:
        logging.error(f"Invalid column name on {db}: {e}")
        return None, f"Invalid column name: {str(e)}"
    finally:
        cursor.close()
        conn.close()


def execute_query(db, sql_query, is_echart=False):
    conn = get_db_connection(db)
    if conn is None:
        logging.error(f"Unable to connect to the database: {db}")
        return None, f"Unable to connect to the database: {db}"
    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(sql_query)
        if sql_query.strip().upper().startswith('SELECT'):
            rows = cursor.fetchall()  # 确保读取所有结果
            converted_rows = convert_decimal(rows)
            if is_echart:
                column_names = list(converted_rows[0].keys()) if converted_rows else []
                if not column_names:  # 无记录时
                    return {"xAxis": "", "yAxis": ""}, None
                elif len(column_names) == 1:  # 只有一列时
                    y_axis_name = column_names[0]
                    yAxis = ";".join([str(row[y_axis_name]) if row[y_axis_name] is not None else "0" for row in converted_rows])
                    return {"xAxis": "", "yAxis": yAxis}, None
                else:  # 两列或以上时
                    x_axis_name = column_names[0]
                    y_axis_name = column_names[1]
                    xAxis = ";".join([str(row[x_axis_name]) if row[x_axis_name] is not None else "-" for row in converted_rows])
                    yAxis = ";".join([str(row[y_axis_name]) if row[y_axis_name] is not None else "0" for row in converted_rows])
                    return {"xAxis": xAxis, "yAxis": yAxis}, None
            else:
                return converted_rows, None
        else:
            conn.commit()
            logging.info(f"Query executed successfully on {db}: {sql_query}")
            return {"message": "Query executed successfully", "changes": cursor.rowcount}, None
    except Error as e:
        logging.error(f"Database error on {db}: {e}")
        return None, str(e)
    except KeyError as e:
        logging.error(f"Invalid column name on {db}: {e}")
        return None, f"Invalid column name: {str(e)}"
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def execute_query_bak(db, sql_query, is_echart=False):
    conn = get_db_connection(db)
    if conn is None:
        logging.error(f"Unable to connect to the database: {db}")
        return None, f"Unable to connect to the database: {db}"
    cursor = None
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(sql_query)
        if sql_query.strip().upper().startswith('SELECT'):
            rows = cursor.fetchall()  # 确保读取所有结果
            converted_rows = convert_decimal(rows)
            if is_echart:
                column_names = list(converted_rows[0].keys()) if converted_rows else []
                if len(column_names) < 2:
                    logging.error("Query result must have at least two columns")
                    return {"xAxis": "", "yAxis": ""}, None
                x_axis_name = column_names[0]
                y_axis_name = column_names[1]
                xAxis = ";".join([str(row[x_axis_name]) for row in converted_rows])
                yAxis = ";".join([str(row[y_axis_name]) for row in converted_rows])
                return {"xAxis": xAxis, "yAxis": yAxis}, None
            else:
                return converted_rows, None
        else:
            conn.commit()
            logging.info(f"Query executed successfully on {db}: {sql_query}")
            return {"message": "Query executed successfully", "changes": cursor.rowcount}, None
    except Error as e:
        logging.error(f"Database error on {db}: {e}")
        return None, str(e)
    except KeyError as e:
        logging.error(f"Invalid column name on {db}: {e}")
        return None, f"Invalid column name: {str(e)}"
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


@app.route('/execute', methods=['POST'])
def execute_sql():
    # 记录入参
    logging.info(f"Received request data: {request.get_json()}")
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    if data is None or 'sql' not in data:
        logging.warning("Missing 'sql' parameter")
        return jsonify({"error": "Missing 'sql' parameter"}), 400
    # 处理 db 参数
    db = data.get('db', 'db_bi')  # 如果没有传入 'db'，则默认为 'db_bi'
    if not db:  # 如果传入的 db 参数为空
        logging.error("Invalid 'db' parameter: empty value")
        return jsonify({"error": "Invalid 'db' parameter: empty value"}), 400
    if db not in DB_CONFIGS:  # 如果 db 参数不在 DB_CONFIGS 中
        logging.error(f"Invalid 'db' parameter: '{db}' not found in DB_CONFIGS")
        return jsonify({"error": f"Invalid 'db' parameter: '{db}' not found in DB_CONFIGS"}), 400
    sql_query = data['sql']
    is_echart = data.get('isEchart', True)
    try:
        sql_query = validate_sql(sql_query)
    except Exception as e:
        logging.error(f"Invalid SQL statement: {e}")
        return jsonify({"error": str(e)}), 400
    result, error = execute_query(db, sql_query, is_echart)
    if error:
        return jsonify({"error": error}), 400
    return jsonify(result)


@app.route('/getPrompt', methods=['POST'])
def get_prompt():
    # 记录入参
    logging.info(f"Received request data: {request.get_json()}")
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    if data is None or 'sql' not in data:
        logging.warning("Missing 'sql' parameter")
        return jsonify({"error": "Missing 'sql' parameter"}), 400
    sql_query = data['sql']
    db = data.get('db', 'db_wiki')  # 如果没有传入 'db'，则默认为 'db_wiki'
    try:
        sql_query = validate_sql(sql_query)
    except Exception as e:
        logging.error(f"Invalid SQL statement: {e}")
        return jsonify({"error": str(e)}), 400
    result, error = execute_query(db, sql_query)
    if error:
        return jsonify({"error": error}), 400
    return jsonify(result)


def parse_input(data):
    """
    解析输入参数，判断是否为 JSON 格式，如果不是则尝试转换为 JSON。
    """
    if isinstance(data, str):
        try:
            # 尝试将字符串解析为 JSON
            return json.loads(data)
        except json.JSONDecodeError:
            logging.warning("Input is not valid JSON")
            return None
    elif isinstance(data, (dict, list)):
        # 如果已经是字典或列表，直接返回
        return data
    else:
        logging.warning("Input is not a valid type (expected str, dict, or list)")
        return None


@app.route('/getPromptByName', methods=['POST'])
def get_prompt_by_name():
    # 获取请求数据
    raw_data = request.get_data(as_text=True)
    logging.info(f"Received request data: {raw_data}")
    # 解析输入参数
    data = parse_input(raw_data)
    if data is None:
        logging.warning("Invalid input data")
        return jsonify({"error": "Invalid input data: expected JSON or JSON-like string"}), 400
    # 检查数据是否为列表
    if not isinstance(data, list):
        logging.warning("Invalid request data: expected a list of index names")
        return jsonify({"error": "Invalid request data: expected a list of index names"}), 400
    # 解析 indexName 列表
    index_names = [item.get("indexName") for item in data if item.get("indexName")]
    if not index_names:
        logging.warning("No valid 'indexName' found in request data")
        return jsonify({"error": "No valid 'indexName' found in request data"}), 400
    try:
        # 只处理第一个 indexName（因为返回的是对象，不是数组）
        index_name = index_names[0]
        # 第一个 SQL：查询 index 信息
        sql_query_1 = f"""
            SELECT a.sql_prompt, a.foreign_keys, a.index_name, GROUP_CONCAT(b.table_name) as tableName 
            FROM idx_index_prompt a
            LEFT JOIN idx_index_link_table b ON a.id = b.index_id AND b.del_flag = 0
            WHERE a.index_name = '{index_name}' AND a.del_flag = 0
            GROUP BY a.id
        """
        # 执行第一个查询
        index_data, error = execute_query('db_wiki', sql_query_1)
        if error:
            logging.error(f"Error executing first query for index '{index_name}': {error}")
            return jsonify({"error": f"Database error: {error}"}), 500
        if not index_data:
            logging.warning(f"No data found for index '{index_name}'")
            return jsonify({"error": f"No data found for index '{index_name}'"}), 404
        # 获取 tableName 信息
        table_names = index_data[0].get("tableName", "").split(",")
        if not table_names:
            logging.warning(f"No table names found for index '{index_name}'")
            return jsonify({"error": f"No table names found for index '{index_name}'"}), 404
        # 初始化 table_schemas 列表
        table_schemas = []
        # 遍历 tableName，执行第二个查询
        for table_name in table_names:
            sql_query_2 = f"""
                SELECT DISTINCT 
                    tbl.table_name_eng AS table_name,
                    tbl.TABLE_ALIAS AS table_desc,
                    col.type AS column_type,
                    col.datalength AS column_len,
                    col.name AS column_name, 
                    col.alias AS column_desc
                FROM idx_source_table_info tbl
                JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
                WHERE tbl.table_name_eng = '{table_name}'
                ORDER BY col.`position`
                limit 2
            """
            # 执行第二个查询
            column_data, error = execute_query('db_wiki', sql_query_2)
            if error:
                logging.error(f"Error executing second query for table '{table_name}': {error}")
                continue
            # 将 table 和 columns 数据添加到 table_schemas 列表
            table_schemas.append({
                "table_name": table_name,
                "columns": [
                    {
                        "column_desc": col["column_desc"],
                        "column_len": col["column_len"],
                        "column_name": col["column_name"],
                        "column_type": col["column_type"],
                        "table_desc": col["table_desc"]
                    }
                    for col in column_data
                ]
            })
        # 构建最终结果（对象类型）
        result = {
            "sql_prompt": index_data[0]["sql_prompt"],
            "foreign_keys": index_data[0]["foreign_keys"],
            "index_name": index_data[0]["index_name"],
            "table_schemas": table_schemas
        }
        # 返回最终结果，确保字符编码正确
        response = jsonify(result)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        return response
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/authentication', methods=['POST'])
def authentication():
    # 记录入参
    logging.info(f"Received authentication request data: {request.get_json()}")
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    logging.info(f"Received authentication request: {data}")
    return jsonify(data)


@app.route('/getIndexTree', methods=['GET'])
def get_index_tree():
    # 记录入参
    sql_query = 'SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0'
    logging.info(f"GetIndexTree data: {sql_query}")
    result, error = execute_query('db_wiki', sql_query)
    if error:
        return jsonify({"error": error}), 400
    return jsonify(result)


if __name__ == '__main__':
    logging.info("Starting Flask application")
    app.run(host='0.0.0.0', port=5000, debug=True)
