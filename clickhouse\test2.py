# sql_tools.py

from flask import Flask, request, jsonify
import mysql.connector

# 数据库连接配置
config = {
    'user': 'llm_readonly',
    'password': 'LLM%2025Rdv1ew',
    'host': '************',
    'database': 'bi_hna_cost',
    'raise_on_warnings': True
}

# 初始化Flask应用
app = Flask(__name__)


# 连接数据库
def connect_to_database():
    try:
        conn = mysql.connector.connect(**config)
        print("Connected to MySQL database")
        return conn
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return None


# 执行SQL查询
def execute_query(conn, sql):
    cursor = conn.cursor()
    try:
        cursor.execute(sql)
        if sql.strip().lower().startswith("select"):
            # 如果是查询操作，返回结果
            result = cursor.fetchall()
            return result
        else:
            # 如果是插入、更新、删除操作，提交事务并返回受影响的行数
            conn.commit()
            return cursor.rowcount
    except mysql.connector.Error as err:
        print(f"Error executing SQL: {err}")
        return None
    finally:
        cursor.close()


# HTTP接口：执行SQL
@app.route('/execute', methods=['POST'])
def execute_sql():
    # 获取请求中的SQL语句
    data = request.json
    if not data or 'sql' not in data:
        return jsonify({"error": "SQL statement is required"}), 400

    sql = data['sql']
    conn = connect_to_database()
    if not conn:
        return jsonify({"error": "Failed to connect to database"}), 500

    # 执行SQL
    result = execute_query(conn, sql)
    conn.close()

    if result is None:
        return jsonify({"error": "Failed to execute SQL"}), 500

    # 返回结果
    return jsonify({"result": result})


# 启动Flask应用
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000)