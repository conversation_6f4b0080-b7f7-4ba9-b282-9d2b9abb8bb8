('D:\\develop\\python\\dist\\scanv_2.exe',
 True,
 False,
 False,
 None,
 None,
 False,
 False,
 '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"><assemblyIdentity type="win32" name="scanv_2" processorArchitecture="amd64" version="1.0.0.0"/><dependency><dependentAssembly><assemblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" language="*" processorArchitecture="*" version="6.0.0.0" publicKeyToken="6595b64144ccf1df"/><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"/></dependentAssembly></dependency><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"><application><supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/><supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/><supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/><supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/><supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/></application></compatibility></assembly>',
 True,
 'scanv_2.pkg',
 [('PYZ-00.pyz', 'D:\\develop\\python\\build\\scanv_2\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'D:\\develop\\python\\build\\scanv_2\\localpycos\\struct.pyo',
   'PYMODULE'),
  ('pyimod01_os_path',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyimod01_os_path.pyc',
   'PYMODULE'),
  ('pyimod02_archive',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyimod02_archive.pyc',
   'PYMODULE'),
  ('pyimod03_importers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyimod03_importers.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('pyi_rth_certifi',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_certifi.py',
   'PYSOURCE'),
  ('scanv_2', 'D:\\develop\\python\\scanv_2.py', 'PYSOURCE'),
  ('python38.dll', 'c:\\program files\\python\\python38.dll', 'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'c:\\program files\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('pywintypes38.dll',
   'c:\\program files\\python\\lib\\site-packages\\win32\\pywintypes38.dll',
   'BINARY'),
  ('pythoncom38.dll',
   'c:\\program '
   'files\\python\\Lib\\site-packages\\pywin32_system32\\pythoncom38.dll',
   'BINARY'),
  ('unicodedata',
   'c:\\program files\\python\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select', 'c:\\program files\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_overlapped',
   'c:\\program files\\python\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio', 'c:\\program files\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_ctypes', 'c:\\program files\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing',
   'c:\\program files\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_queue', 'c:\\program files\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('pyexpat', 'c:\\program files\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal', 'c:\\program files\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_testcapi', 'c:\\program files\\python\\DLLs\\_testcapi.pyd', 'EXTENSION'),
  ('_tkinter', 'c:\\program files\\python\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('_elementtree',
   'c:\\program files\\python\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32evtlog',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32api',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_hashlib', 'c:\\program files\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma', 'c:\\program files\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2', 'c:\\program files\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl', 'c:\\program files\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('win32trace',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32ui',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('_win32sysloader',
   'c:\\program files\\python\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32com.shell.shell',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32wnet',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('sqlalchemy.cresultproxy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cresultproxy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cutils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cutils.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cprocessors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cprocessors.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3', 'c:\\program files\\python\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_cffi_backend',
   'c:\\program '
   'files\\python\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography.hazmat.bindings._openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt._bcrypt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\bcrypt\\_bcrypt.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_mysql_connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\_mysql_connector.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('MySQLdb._mysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\_mysql.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_socket', 'c:\\program files\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libffi-7.dll', 'c:\\program files\\python\\DLLs\\libffi-7.dll', 'BINARY'),
  ('tcl86t.dll', 'c:\\program files\\python\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'c:\\program files\\python\\DLLs\\tk86t.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\program files\\python\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\program files\\python\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('mfc140u.dll',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('sqlite3.dll', 'c:\\program files\\python\\DLLs\\sqlite3.dll', 'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'D:\\develop\\python\\build\\scanv_2\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tk\\tclIndex', 'c:\\program files\\python\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('lib2to3\\PatternGrammar.txt',
   'c:\\program files\\python\\lib\\lib2to3\\PatternGrammar.txt',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\RECORD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\RECORD',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\AUTHORS.rst',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\AUTHORS.rst',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\init.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tk\\text.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\package.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tk\\focus.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tk\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\METADATA',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\METADATA',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('Include\\pyconfig.h',
   'c:\\program files\\python\\Include\\pyconfig.h',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.APACHE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('tk\\images\\README',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tk\\images\\logo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tk\\choosedir.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('lib2to3\\tests\\data\\README',
   'c:\\program files\\python\\lib\\lib2to3\\tests\\data\\README',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tk\\tk.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tk\\scale.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tk\\obsolete.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tk\\comdlg.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tclIndex', 'c:\\program files\\python\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\safe.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tzdata\\ROK',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\optMenu.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\clock.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\INSTALLER',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\INSTALLER',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tk\\menu.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\word.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tk\\unsupported.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\license.terms',
   'c:\\program files\\python\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\auto.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tk\\clrpick.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tk\\console.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tk\\safetk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\history.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tk\\bgerror.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tk\\dialog.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tk\\icons.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tk\\entry.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tcl\\msgs\\kw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.BSD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.BSD',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\top_level.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tk\\palette.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tm.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tk\\msgbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\parray.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tk\\tearoff.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tk\\megawidget.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.PSF',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.PSF',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tk\\iconlist.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tk\\listbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\WHEEL',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\WHEEL',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('lib2to3\\Grammar.txt',
   'c:\\program files\\python\\lib\\lib2to3\\Grammar.txt',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('scanv_2.exe.manifest',
   'D:\\develop\\python\\build\\scanv_2\\scanv_2.exe.manifest',
   'BINARY'),
  ('pyi-windows-manifest-filename scanv_2.exe.manifest', '', 'OPTION')],
 [],
 False,
 False,
 1737645387,
 [('run.exe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit\\run.exe',
   'EXECUTABLE')])
