import os
from pydub import AudioSegment


def batch_convert_m4a_to_mp3(input_dir, output_dir=None, bitrate="192k"):
    """
    批量将目录中的M4A文件转换为MP3格式

    参数:
        input_dir (str): 包含M4A文件的目录路径
        output_dir (str, 可选): 输出目录。如果为None，则使用输入目录
        bitrate (str): 输出MP3的比特率，默认为"192k"
    """
    # 检查输入目录是否存在
    if not os.path.isdir(input_dir):
        raise NotADirectoryError(f"输入目录不存在: {input_dir}")

    # 如果未指定输出目录，则使用输入目录
    if output_dir is None:
        output_dir = input_dir
    else:
        os.makedirs(output_dir, exist_ok=True)

    # 遍历输入目录中的M4A文件
    for filename in os.listdir(input_dir):
        if filename.lower().endswith(".m4a"):
            input_path = os.path.join(input_dir, filename)
            output_filename = os.path.splitext(filename)[0] + ".mp3"
            output_path = os.path.join(output_dir, output_filename)

            try:
                # 转换文件
                audio = AudioSegment.from_file(input_path, format="m4a")
                audio.export(output_path, format="mp3", bitrate=bitrate)
                print(f"转换成功: {filename} -> {output_filename}")
            except Exception as e:
                print(f"转换失败 {filename}: {str(e)}")


if __name__ == "__main__":
    import argparse

    # 设置命令行参数
    parser = argparse.ArgumentParser(description="批量将M4A文件转换为MP3格式")
    parser.add_argument("input_dir", help="包含M4A文件的目录路径")
    parser.add_argument("-o", "--output_dir", help="输出的MP3文件目录（可选）")
    parser.add_argument("-b", "--bitrate", default="192k",
                        help="MP3比特率（如128k, 192k, 256k等），默认为192k")

    args = parser.parse_args()

    # 执行批量转换
    try:
        batch_convert_m4a_to_mp3(args.input_dir, args.output_dir, args.bitrate)
    except Exception as e:
        print(f"批量转换失败: {str(e)}")