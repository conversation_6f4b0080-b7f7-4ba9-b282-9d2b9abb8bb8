
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running you program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported from within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
           yourself tracking down the missing module. Thanks!

missing module named 'org.python' - imported by copy (optional), setuptools.sandbox (conditional), xml.sax (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), PyInstaller.loader.pyimod02_archive (delayed, conditional)
missing module named urllib.getproxies_environment - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.proxy_bypass_environment - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.proxy_bypass - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.getproxies - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.urlencode - imported by urllib (conditional), requests.compat (conditional)
missing module named urllib.pathname2url - imported by urllib (conditional), PyInstaller.lib.modulegraph._compat (conditional)
missing module named urllib.unquote_plus - imported by urllib (conditional), sqlalchemy.util.compat (conditional), requests.compat (conditional)
missing module named urllib.unquote - imported by urllib (conditional), sqlalchemy.util.compat (conditional), requests.compat (conditional)
missing module named urllib.quote_plus - imported by urllib (conditional), sqlalchemy.util.compat (conditional), requests.compat (conditional)
missing module named urllib.quote - imported by urllib (conditional), sqlalchemy.util.compat (conditional), requests.compat (conditional)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named termios - imported by tty (top-level), getpass (optional), scapy.utils (delayed, optional), psutil._compat (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named resource - imported by posix (top-level), test.support (optional)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), requests.utils (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), rlcompleter (optional), scapy.main (delayed, conditional, optional)
missing module named org - imported by pickle (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed), distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), psutil (optional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional), C:\Program Files\python\Lib\site-packages\PyInstaller\loader\rthooks\pyi_rth_win32comgenpy.py (top-level)
missing module named pyimod03_importers - imported by PyInstaller.loader.pyimod02_archive (delayed, conditional), C:\Program Files\python\Lib\site-packages\PyInstaller\loader\rthooks\pyi_rth_pkgres.py (top-level)
missing module named 'com.sun' - imported by pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.requirements (top-level), pkg_resources._vendor.packaging.markers (top-level)
missing module named _uuid - imported by uuid (optional)
missing module named __builtin__ - imported by scapy.config (delayed, optional), pkg_resources._vendor.pyparsing (conditional), setuptools._vendor.pyparsing (conditional)
missing module named ordereddict - imported by pkg_resources._vendor.pyparsing (optional), setuptools._vendor.pyparsing (optional)
missing module named StringIO - imported by six (conditional), sqlalchemy.util.compat (conditional), setuptools._vendor.six (conditional), PyInstaller.lib.modulegraph._compat (conditional), PyInstaller.lib.modulegraph.zipio (conditional), urllib3.packages.six (conditional), requests.compat (conditional), pkg_resources._vendor.six (conditional)
missing module named _manylinux - imported by pkg_resources._vendor.packaging.tags (delayed, optional), setuptools._vendor.packaging.tags (delayed, optional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.six.moves' - imported by pkg_resources (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named pkg_resources.extern.six - imported by pkg_resources.extern (top-level), pkg_resources (top-level), pkg_resources.py31compat (top-level)
missing module named 'multiprocessing.forking' - imported by C:\Program Files\python\Lib\site-packages\PyInstaller\loader\rthooks\pyi_rth_multiprocessing.py (optional)
missing module named cPickle - imported by sqlalchemy.util.compat (conditional, optional), pycparser.ply.yacc (delayed, optional), sqlalchemy.testing.util (delayed, conditional, optional)
missing module named copy_reg - imported by cStringIO (top-level), cPickle (top-level)
missing module named urlparse - imported by sqlalchemy.util.compat (conditional), requests.compat (conditional)
missing module named cStringIO - imported by sqlalchemy.util.compat (conditional), cPickle (top-level), cffi.ffiplatform (optional)
missing module named ConfigParser - imported by sqlalchemy.util.compat (conditional)
missing module named _dummy_threading - imported by dummy_threading (optional)
missing module named Sybase - imported by sqlalchemy.dialects.sybase.pysybase (delayed)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named pysqlcipher - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named 'com.ziclix' - imported by sqlalchemy.dialects.oracle.zxjdbc (delayed), sqlalchemy.dialects.postgresql.zxjdbc (delayed)
missing module named com - imported by sqlalchemy.connectors.zxJDBC (delayed)
missing module named postgresql - imported by sqlalchemy.dialects.postgresql.pypostgresql (delayed)
missing module named pgdb - imported by sqlalchemy.dialects.postgresql.pygresql (delayed)
missing module named 'java.sql' - imported by sqlalchemy.dialects.oracle.zxjdbc (delayed)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'opentelemetry.semconv' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.sdk' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named opentelemetry - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.context_propagation (conditional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'requests_toolbelt.adapters' - imported by dns.query (optional)
missing module named requests_toolbelt - imported by dns.query (optional)
missing module named Cookie - imported by requests.compat (conditional)
missing module named cookielib - imported by requests.compat (conditional)
missing module named urllib2 - imported by requests.compat (conditional)
missing module named simplejson - imported by requests.compat (optional)
missing module named 'backports.ssl_match_hostname' - imported by setuptools.ssl_support (optional), urllib3.packages.ssl_match_hostname (optional)
missing module named Queue - imported by urllib3.util.queue (conditional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), scapy.layers.http (optional)
missing module named "'urllib3.packages.six.moves.urllib'.parse" - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.connectionpool (top-level), urllib3.util.response (top-level), 'urllib3.packages.six.moves.urllib' (top-level), urllib3.response (top-level), urllib3.util.queue (top-level)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named six.moves.range - imported by six.moves (top-level), cryptography.hazmat.backends.openssl.backend (top-level)
runtime module named six.moves - imported by cryptography.hazmat.backends.openssl.backend (top-level), cryptography.x509.general_name (top-level)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'opentelemetry.trace' - imported by mysql.connector.opentelemetry.context_propagation (conditional)
missing module named 'google.storage' - imported by sqlalchemy.dialects.mysql.gaerdbms (delayed, conditional)
missing module named 'google.appengine' - imported by sqlalchemy.dialects.mysql.gaerdbms (delayed, conditional)
missing module named 'mx.ODBC' - imported by sqlalchemy.connectors.mxodbc (delayed, conditional)
missing module named clr - imported by adodbapi.adodbapi (conditional)
missing module named System - imported by adodbapi.apibase (conditional), adodbapi.is64bit (delayed, conditional), adodbapi.adodbapi (conditional)
missing module named mx - imported by adodbapi.apibase (optional)
missing module named exceptions - imported by adodbapi.apibase (conditional)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed), sqlalchemy (top-level)
missing module named sets - imported by MySQLdb (optional)
missing module named pysqlite2 - imported by sqlalchemy.dialects.sqlite.pysqlite (delayed, conditional, optional), sqlalchemy (top-level)
missing module named matplotlib - imported by scapy.libs.matplot (optional), scapy.layers.inet (delayed)
missing module named 'matplotlib.collections' - imported by scapy.layers.inet (delayed)
missing module named cartopy - imported by scapy.layers.inet (delayed, optional)
missing module named 'geoip2.errors' - imported by scapy.layers.inet (delayed, optional)
missing module named geoip2 - imported by scapy.layers.inet (delayed, optional)
missing module named vpython - imported by scapy.layers.inet (delayed)
missing module named 'matplotlib.lines' - imported by scapy.libs.matplot (optional)
missing module named prompt_toolkit - imported by scapy.utils (delayed, conditional, optional), scapy.packet (delayed, conditional, optional), scapy.layers.kerberos (delayed, conditional, optional), scapy.layers.smbclient (delayed, conditional)
missing module named 'cryptography.hazmat.decrepit' - imported by scapy.layers.tls.crypto.cipher_block (conditional, optional), scapy.layers.tls.crypto.cipher_stream (conditional, optional), scapy.libs.rfc3961 (optional), scapy.layers.ntlm (delayed, optional), scapy.layers.ipsec (conditional, optional), scapy.layers.dot11 (conditional, optional)
missing module named zstandard - imported by scapy.layers.http (optional)
missing module named lzw - imported by scapy.layers.http (optional)
missing module named fcntl - imported by scapy.arch.linux (top-level), scapy.arch.bpf.core (top-level), scapy.arch.bpf.supersocket (top-level), scapy.arch.libpcap (conditional), scapy.arch.unix (top-level), scapy.utils (delayed, optional), scapy.layers.tuntap (top-level), psutil._compat (delayed, optional)
missing module named 'bpython.curtsies' - imported by scapy.main (delayed, conditional)
missing module named 'ptpython.repl' - imported by scapy.main (delayed, conditional)
missing module named traitlets - imported by scapy.main (delayed, conditional, optional)
missing module named ptpython - imported by scapy.main (delayed, conditional)
missing module named bpython - imported by scapy.main (delayed, conditional)
missing module named IPython - imported by scapy.arch.windows (delayed, optional), scapy.main (delayed, conditional, optional)
missing module named 'prompt_toolkit.completion' - imported by scapy.utils (delayed)
missing module named 'prompt_toolkit.formatted_text' - imported by scapy.packet (delayed, conditional)
missing module named 'prompt_toolkit.shortcuts' - imported by scapy.packet (delayed, conditional)
missing module named pyx - imported by scapy.base_classes (conditional, optional), scapy.plist (optional), scapy.libs.test_pyx (optional), scapy.packet (optional)
missing module named 'IPython.terminal' - imported by scapy.themes (delayed, optional)
missing module named __pypy__ - imported by scapy.config (delayed, optional)
