#!/bin/bash

# 环境检测脚本 - CentOS 7
# 检查Python环境和必要依赖包

# 目标Python版本 (CentOS 7默认仓库中的版本)
TARGET_PYTHON_VERSION="3.6.8"

# 国内Python源列表
PYPI_SOURCES=("https://mirrors.aliyun.com/pypi/simple/" "https://pypi.doubanio.com/simple/" "https://pypi.tuna.tsinghua.edu.cn/simple/")
SELECTED_SOURCE=${PYPI_SOURCES[0]}

# 虚拟环境名称
VENV_NAME=".venv"

# 必要的Python包
REQUIRED_PACKAGES=("opencv-python" "flask")

# 打印函数，带颜色
function print_info() {
    echo -e "\033[32m[INFO] $1\033[0m"
}

function print_warn() {
    echo -e "\033[33m[WARN] $1\033[0m"
}

function print_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查是否以root用户运行
function check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 检查操作系统是否为CentOS 7
function check_os() {
    if [ -f /etc/redhat-release ]; then
        OS_VERSION=$(cat /etc/redhat-release)
        if [[ $OS_VERSION != *"CentOS Linux release 7"* ]]; then
            print_warn "此脚本专为CentOS 7设计，您的系统是: $OS_VERSION"
            print_warn "继续运行可能会出现问题"
            read -p "是否继续? (y/n): " choice
            if [ "$choice" != "y" ] && [ "$choice" != "Y" ]; then
                exit 1
            fi
        fi
    else
        print_error "无法确定操作系统版本，此脚本专为CentOS 7设计"
        exit 1
    fi
}

# 检查Python 3是否安装
function check_python() {
    # 检查是否存在Python 3
    if command -v python3 &> /dev/null; then
        PYTHON3_VERSION=$(python3 --version 2>&1)
        print_info "已安装Python 3: $PYTHON3_VERSION"
        return 0
    else
        # 检查是否存在Python命令
        if command -v python &> /dev/null; then
            PYTHON_VERSION=$(python --version 2>&1)
            print_warn "未找到Python 3，但找到了: $PYTHON_VERSION"
            print_warn "此脚本需要Python 3，请安装Python 3"
        else
            print_error "未找到Python命令"
        fi
        return 1
    fi
}

# 检查Python版本是否满足要求
function check_python_version() {
    if command -v python3 &> /dev/null; then
        # 提取版本号数字部分
        CURRENT_PYTHON3_VERSION=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:3])))" )
        print_info "当前Python 3版本: $CURRENT_PYTHON3_VERSION"
        print_info "目标Python版本: $TARGET_PYTHON_VERSION"
        
        # 比较版本号
        if [ "$(printf '%s\n' "$TARGET_PYTHON_VERSION" "$CURRENT_PYTHON3_VERSION" | sort -V | head -n1)" != "$TARGET_PYTHON_VERSION" ]; then
            print_warn "Python 3版本低于目标版本"
            return 1
        else
            print_info "Python 3版本满足要求"
            return 0
        fi
    else
        print_error "未找到Python 3"
        return 1
    fi
}

# 修复yum依赖问题
function fix_yum_python_dependency() {
    print_info "检查并修复yum的Python依赖..."

    # 检查是否存在python2
    if [ -f "/usr/bin/python2" ]; then
        print_info "找到python2，创建python符号链接"
        ln -sf /usr/bin/python2 /usr/bin/python
    elif [ -f "/usr/bin/python2.7" ]; then
        print_info "找到python2.7，创建python符号链接"
        ln -sf /usr/bin/python2.7 /usr/bin/python
    else
        print_error "未找到python2或python2.7，无法修复yum依赖"
        print_info "尝试使用rpm直接安装python2..."

        # 尝试使用rpm安装python2
        if command -v rpm &> /dev/null; then
            # 下载并安装python2 (适用于CentOS 7)
            print_info "正在下载python2 rpm包..."
            cd /tmp
            wget -q http://mirror.centos.org/centos/7/os/x86_64/Packages/python-2.7.5-90.el7.x86_64.rpm
            if [ -f "python-2.7.5-90.el7.x86_64.rpm" ]; then
                rpm -ivh python-2.7.5-90.el7.x86_64.rpm
                if [ -f "/usr/bin/python2.7" ]; then
                    ln -sf /usr/bin/python2.7 /usr/bin/python
                    print_info "python2安装成功"
                else
                    print_error "python2安装失败"
                    return 1
                fi
            else
                print_error "无法下载python2 rpm包"
                return 1
            fi
        else
            print_error "rpm命令不可用，无法安装python2"
            return 1
        fi
    fi

    # 验证yum是否可以正常工作
    if yum --version &> /dev/null; then
        print_info "yum修复成功"
        return 0
    else
        print_error "yum仍然无法正常工作"
        return 1
    fi
}

# 安装或升级Python到目标版本
function install_or_upgrade_python() {
    print_info "开始安装/升级Python到$TARGET_PYTHON_VERSION..."

    # 首先修复yum的Python依赖问题
    if ! yum --version &> /dev/null; then
        print_warn "yum无法正常工作，尝试修复..."
        if ! fix_yum_python_dependency; then
            print_error "无法修复yum，尝试其他安装方法..."
            return install_python_alternative
        fi
    fi

    # 使用yum安装Python 3.6和pip
    yum install -y python36 python36-pip
    
    # 检查是否安装成功
    if command -v python3.6 &> /dev/null; then
        PYTHON_VERSION=$(python3.6 --version 2>&1)
        print_info "Python 3.6安装成功: $PYTHON_VERSION"
        
        # 创建符号链接
        ln -sf /usr/bin/python3.6 /usr/bin/python3
        ln -sf /usr/bin/pip3.6 /usr/bin/pip3
        print_info "已创建Python 3.6符号链接"
        
        # 确保pip已安装
        if command -v pip3 &> /dev/null; then
            print_info "pip已安装: $(pip3 --version 2>&1)"
            # 升级pip
            pip3 install --upgrade pip -i $SELECTED_SOURCE
        else
            print_error "pip安装失败"
            # 尝试手动安装pip
            print_info "尝试手动安装pip..."
            curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
            python3.6 get-pip.py
            rm -f get-pip.py
            
            if command -v pip3 &> /dev/null; then
                print_info "pip手动安装成功: $(pip3 --version 2>&1)"
            else
                print_error "pip手动安装也失败"
                return 1
            fi
        fi
        
        return 0
    else
        print_error "Python 3.6安装失败"
        return 1
    fi
}

# 备用Python安装方法（当yum不可用时）
function install_python_alternative() {
    print_info "使用备用方法安装Python 3.6..."

    # 检查是否有网络连接
    if ! ping -c 1 ******* &> /dev/null; then
        print_error "无网络连接，无法下载Python"
        return 1
    fi

    # 安装编译依赖
    print_info "尝试安装编译依赖..."
    if command -v rpm &> /dev/null; then
        # 使用rpm安装基本的编译工具
        cd /tmp
        wget -q http://mirror.centos.org/centos/7/os/x86_64/Packages/gcc-4.8.5-44.el7.x86_64.rpm
        wget -q http://mirror.centos.org/centos/7/os/x86_64/Packages/make-3.82-24.el7.x86_64.rpm
        wget -q http://mirror.centos.org/centos/7/os/x86_64/Packages/zlib-devel-1.2.7-20.el7_9.x86_64.rpm

        for rpm_file in *.rpm; do
            if [ -f "$rpm_file" ]; then
                rpm -ivh "$rpm_file" 2>/dev/null || true
            fi
        done
    fi

    # 下载并编译安装Python 3.6
    print_info "下载Python 3.6源码..."
    cd /tmp
    wget -q https://www.python.org/ftp/python/3.6.8/Python-3.6.8.tgz

    if [ -f "Python-3.6.8.tgz" ]; then
        print_info "解压并编译Python 3.6..."
        tar -xzf Python-3.6.8.tgz
        cd Python-3.6.8

        ./configure --prefix=/usr/local/python3.6 --enable-optimizations
        make -j$(nproc)
        make altinstall

        # 创建符号链接
        ln -sf /usr/local/python3.6/bin/python3.6 /usr/bin/python3.6
        ln -sf /usr/local/python3.6/bin/python3.6 /usr/bin/python3
        ln -sf /usr/local/python3.6/bin/pip3.6 /usr/bin/pip3.6
        ln -sf /usr/local/python3.6/bin/pip3.6 /usr/bin/pip3

        # 验证安装
        if command -v python3.6 &> /dev/null; then
            print_info "Python 3.6编译安装成功: $(python3.6 --version)"
            return 0
        else
            print_error "Python 3.6编译安装失败"
            return 1
        fi
    else
        print_error "无法下载Python 3.6源码"
        return 1
    fi
}

# 创建并激活虚拟环境
function create_venv() {
    print_info "创建虚拟环境..."
    
    # 尝试使用系统安装的Python 3.6
    if command -v /usr/bin/python3.6 &> /dev/null; then
        print_info "使用Python 3.6创建虚拟环境"
        /usr/bin/python3.6 -m venv $VENV_NAME
    elif command -v /usr/bin/python3 &> /dev/null; then
        # 检查python3的实际版本
        PYTHON3_VERSION=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:3])))" 2>/dev/null || echo "0.0.0")
        if [[ "$PYTHON3_VERSION" == 3.6.* ]]; then
            print_info "使用Python 3.6创建虚拟环境"
            /usr/bin/python3 -m venv $VENV_NAME
        else
            print_warn "未找到Python 3.6，系统默认Python版本为$PYTHON3_VERSION"
            print_warn "这可能导致虚拟环境创建失败"
            /usr/bin/python3 -m venv $VENV_NAME
        fi
    else
        print_error "未找到有效的Python解释器"
        return 1
    fi
    
    if [ -d "$VENV_NAME" ]; then
        print_info "虚拟环境创建成功: $VENV_NAME"
        # 激活虚拟环境的命令提示
        print_info "请运行以下命令激活虚拟环境:"
        print_info "source $VENV_NAME/bin/activate"
        return 0
    else
        print_error "虚拟环境创建失败"
        # 输出详细错误信息帮助调试
        if [ -f "/usr/bin/python3.6" ]; then
            print_info "/usr/bin/python3.6存在，但创建虚拟环境失败"
            print_info "尝试手动运行: /usr/bin/python3.6 -m venv $VENV_NAME 查看详细错误"
        elif [ -n "$PYTHON36_PATH" ]; then
            print_info "找到Python解释器: $PYTHON36_PATH，但创建虚拟环境失败"
            print_info "尝试手动运行: $PYTHON36_PATH -m venv $VENV_NAME 查看详细错误"
        fi
        return 1
    fi
}

# 在虚拟环境中安装Python包
function install_python_packages_in_venv() {
    if [ -d "$VENV_NAME" ]; then
        print_info "在虚拟环境中安装Python包..."
        
        # 构建激活虚拟环境并安装包的命令
        INSTALL_COMMAND="source $VENV_NAME/bin/activate && pip install --upgrade pip -i $SELECTED_SOURCE && pip install ${REQUIRED_PACKAGES[@]} -i $SELECTED_SOURCE"
        
        # 执行命令
        eval $INSTALL_COMMAND
        
        # 检查是否安装成功
        for package in "${REQUIRED_PACKAGES[@]}"; do
            if ! $VENV_NAME/bin/python -c "import $package" &> /dev/null; then
                print_error "Python包 '$package' 在虚拟环境中安装失败"
                return 1
            fi
        done
        
        print_info "所有Python包在虚拟环境中安装成功"
        return 0
    else
        print_error "虚拟环境不存在，请先创建虚拟环境"
        return 1
    fi
}

# 检查pip是否安装
function check_pip() {
    if command -v pip3 &> /dev/null; then
        PIP_VERSION=$(pip3 --version 2>&1)
        print_info "已安装pip: $PIP_VERSION"
        return 0
    else
        print_error "未找到pip3"
        return 1
    fi
}

# 检查Python包是否安装
function check_python_package() {
    local package=$1
    local python_cmd="python3"
    
    # 如果存在虚拟环境，使用虚拟环境中的Python
    if [ -d "$VENV_NAME" ]; then
        python_cmd="$VENV_NAME/bin/python"
    fi
    
    if $python_cmd -c "import $package" &> /dev/null; then
        print_info "Python包 '$package' 已安装"
        return 0
    else
        print_warn "Python包 '$package' 未安装"
        return 1
    fi
}

# 安装Python包
function install_python_packages() {
    print_info "使用国内源: $SELECTED_SOURCE"
    local pip_cmd="pip3"
    
    # 如果存在虚拟环境，使用虚拟环境中的pip
    if [ -d "$VENV_NAME" ]; then
        pip_cmd="$VENV_NAME/bin/pip"
    fi
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if ! check_python_package $package; then
            print_info "安装Python包: $package"
            $pip_cmd install $package -i $SELECTED_SOURCE
            if check_python_package $package; then
                print_info "Python包 '$package' 安装成功"
            else
                print_error "Python包 '$package' 安装失败"
                exit 1
            fi
        fi
    done
}

# 主函数
function main() {
    print_info "===== 环境检测脚本开始 ====="
    
    # 检查root权限
    check_root
    
    # 检查操作系统
    check_os
    
    # 首先检查Python是否存在
    if ! check_python; then
        read -p "是否安装Python 3? (y/n): " choice
        if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
            print_info "开始安装Python 3..."
            if ! install_or_upgrade_python; then
                print_error "Python 3安装失败"
                exit 1
            fi
        else
            print_error "没有Python环境，脚本退出"
            exit 1
        fi
    fi

    # 检查Python版本是否满足要求
    if ! check_python_version; then
        print_warn "Python版本不满足要求"
        read -p "是否安装/升级Python到$TARGET_PYTHON_VERSION? (y/n): " choice
        if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
            if ! install_or_upgrade_python; then
                print_error "Python安装/升级失败，脚本退出"
                exit 1
            fi
            # 安装后重新检查Python
            if ! check_python_version; then
                print_error "Python安装后版本仍不满足要求，脚本退出"
                exit 1
            fi
        else
            print_warn "继续使用当前Python版本，可能导致依赖包安装失败"
        fi
    fi
    
    # 检查并安装pip
    if ! check_pip; then
        read -p "是否安装pip? (y/n): " choice
        if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
            install_pip
        else
            exit 1
        fi
    fi
    
    # 选择国内源
    print_info "请选择国内Python源(1-3):"
    for i in "${!PYPI_SOURCES[@]}"; do
        echo "$((i+1)). ${PYPI_SOURCES[$i]}"
    done
    read -p "请输入选择(默认1): " source_choice
    if [[ -n "$source_choice" ]] && ((source_choice >= 1 && source_choice <= ${#PYPI_SOURCES[@]})); then
        SELECTED_SOURCE=${PYPI_SOURCES[$((source_choice-1))]}
    fi
    print_info "已选择源: $SELECTED_SOURCE"
    
    # 创建虚拟环境
    if [ ! -d "$VENV_NAME" ]; then
        read -p "是否创建虚拟环境? (y/n): " choice
        if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
            if ! create_venv; then
                print_error "虚拟环境创建失败，脚本退出"
                exit 1
            fi
        else
            print_warn "未创建虚拟环境，将直接安装依赖包到系统Python环境"
            # 检查并安装Python包到系统环境
            read -p "是否检查并安装必要的Python包? (y/n): " choice
            if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
                install_python_packages
            else
                # 仅检查不安装
                for package in "${REQUIRED_PACKAGES[@]}"; do
                    check_python_package $package
                done
            fi
            
            # 检查完成
            print_info "===== 环境检测脚本完成 ====="
            print_info "请运行 ./start_service.sh 启动服务"
            exit 0
        fi
    else
        print_info "虚拟环境已存在: $VENV_NAME"
    fi
    
    # 在虚拟环境中安装Python包
    read -p "是否在虚拟环境中安装必要的Python包? (y/n): " choice
    if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
        if ! install_python_packages_in_venv; then
            print_error "Python包安装失败，脚本退出"
            exit 1
        fi
    else
        # 仅检查不安装
        for package in "${REQUIRED_PACKAGES[@]}"; do
            if ! $VENV_NAME/bin/python -c "import $package" &> /dev/null; then
                print_warn "Python包 '$package' 在虚拟环境中未安装"
            else
                print_info "Python包 '$package' 在虚拟环境中已安装"
            fi
        done
    fi
    
    # 检查完成
    print_info "===== 环境检测脚本完成 ====="
    print_info "请先激活虚拟环境: source $VENV_NAME/bin/activate"
    print_info "然后运行 ./start_service.sh 启动服务"
}

# 执行主函数
main