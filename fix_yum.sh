#!/bin/bash

# 修复yum的Python依赖问题
# 适用于CentOS 7系统

# 打印函数，带颜色
function print_info() {
    echo -e "\033[32m[INFO] $1\033[0m"
}

function print_warn() {
    echo -e "\033[33m[WARN] $1\033[0m"
}

function print_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查是否以root用户运行
function check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 修复yum的Python依赖问题
function fix_yum_python() {
    print_info "开始修复yum的Python依赖问题..."
    
    # 检查当前yum状态
    if yum --version &> /dev/null; then
        print_info "yum工作正常，无需修复"
        return 0
    fi
    
    print_warn "yum无法正常工作，开始修复..."
    
    # 方法1: 查找并链接现有的Python2
    if [ -f "/usr/bin/python2" ]; then
        print_info "找到python2，创建符号链接"
        ln -sf /usr/bin/python2 /usr/bin/python
    elif [ -f "/usr/bin/python2.7" ]; then
        print_info "找到python2.7，创建符号链接"
        ln -sf /usr/bin/python2.7 /usr/bin/python
    else
        print_warn "未找到python2，尝试其他方法..."
        
        # 方法2: 使用rpm直接安装python2
        print_info "尝试使用rpm安装python2..."
        
        # 创建临时目录
        TEMP_DIR="/tmp/python_fix_$$"
        mkdir -p "$TEMP_DIR"
        cd "$TEMP_DIR"
        
        # 下载python2 rpm包
        print_info "下载python2 rpm包..."
        if command -v wget &> /dev/null; then
            wget -q http://mirror.centos.org/centos/7/os/x86_64/Packages/python-2.7.5-90.el7.x86_64.rpm
        elif command -v curl &> /dev/null; then
            curl -s -o python-2.7.5-90.el7.x86_64.rpm http://mirror.centos.org/centos/7/os/x86_64/Packages/python-2.7.5-90.el7.x86_64.rpm
        else
            print_error "未找到wget或curl，无法下载rpm包"
            return 1
        fi
        
        # 安装python2
        if [ -f "python-2.7.5-90.el7.x86_64.rpm" ]; then
            print_info "安装python2..."
            rpm -ivh python-2.7.5-90.el7.x86_64.rpm --force --nodeps
            
            # 创建符号链接
            if [ -f "/usr/bin/python2.7" ]; then
                ln -sf /usr/bin/python2.7 /usr/bin/python
                print_info "python2安装成功"
            else
                print_error "python2安装失败"
                return 1
            fi
        else
            print_error "无法下载python2 rpm包"
            return 1
        fi
        
        # 清理临时文件
        cd /
        rm -rf "$TEMP_DIR"
    fi
    
    # 验证修复结果
    if yum --version &> /dev/null; then
        print_info "yum修复成功！"
        return 0
    else
        print_error "yum修复失败"
        return 1
    fi
}

# 主函数
function main() {
    print_info "===== yum修复脚本开始 ====="
    
    # 检查root权限
    check_root
    
    # 修复yum
    if fix_yum_python; then
        print_info "===== yum修复完成 ====="
        print_info "现在可以正常使用yum命令了"
        print_info "请重新运行 ./check_env.sh"
    else
        print_error "===== yum修复失败 ====="
        print_error "请手动检查系统Python环境"
        exit 1
    fi
}

# 执行主函数
main
