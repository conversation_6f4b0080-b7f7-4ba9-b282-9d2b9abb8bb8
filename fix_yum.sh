#!/bin/bash

# 修复yum的Python依赖问题并安装Python 3.6
# 适用于CentOS 7系统
# 使用国内高速镜像源

# 目标Python版本 (与check_env.sh保持一致)
TARGET_PYTHON_VERSION="3.6.8"

# 国内CentOS镜像源列表
CENTOS_MIRRORS=(
    "https://mirrors.aliyun.com/centos"
    "https://mirrors.tuna.tsinghua.edu.cn/centos"
    "https://mirrors.ustc.edu.cn/centos"
    "https://mirror.lzu.edu.cn/centos"
)

# 国内Python源列表 (与check_env.sh保持一致)
PYPI_SOURCES=(
    "https://mirrors.aliyun.com/pypi/simple/"
    "https://pypi.doubanio.com/simple/"
    "https://mirrors.tuna.tsinghua.edu.cn/pypi/simple/"
)

# 默认选择阿里云镜像
SELECTED_CENTOS_MIRROR=${CENTOS_MIRRORS[0]}
SELECTED_PYPI_SOURCE=${PYPI_SOURCES[0]}

# 打印函数，带颜色
function print_info() {
    echo -e "\033[32m[INFO] $1\033[0m"
}

function print_warn() {
    echo -e "\033[33m[WARN] $1\033[0m"
}

function print_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查是否以root用户运行
function check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 配置CentOS国内镜像源
function setup_centos_mirrors() {
    print_info "配置CentOS国内镜像源..."

    # 备份原始源文件
    if [ -f "/etc/yum.repos.d/CentOS-Base.repo" ]; then
        cp /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup.$(date +%Y%m%d_%H%M%S)
        print_info "已备份原始CentOS源配置"
    fi

    # 创建阿里云CentOS 7源配置
    cat > /etc/yum.repos.d/CentOS-Base.repo << 'EOF'
# CentOS-Base.repo - 阿里云镜像源
[base]
name=CentOS-$releasever - Base - mirrors.aliyun.com
failovermethod=priority
baseurl=https://mirrors.aliyun.com/centos/$releasever/os/$basearch/
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[updates]
name=CentOS-$releasever - Updates - mirrors.aliyun.com
failovermethod=priority
baseurl=https://mirrors.aliyun.com/centos/$releasever/updates/$basearch/
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[extras]
name=CentOS-$releasever - Extras - mirrors.aliyun.com
failovermethod=priority
baseurl=https://mirrors.aliyun.com/centos/$releasever/extras/$basearch/
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7

[centosplus]
name=CentOS-$releasever - Plus - mirrors.aliyun.com
failovermethod=priority
baseurl=https://mirrors.aliyun.com/centos/$releasever/centosplus/$basearch/
gpgcheck=1
enabled=0
gpgkey=https://mirrors.aliyun.com/centos/RPM-GPG-KEY-CentOS-7
EOF

    # 添加EPEL源（用于安装Python 3.6）
    cat > /etc/yum.repos.d/epel.repo << 'EOF'
[epel]
name=Extra Packages for Enterprise Linux 7 - $basearch - mirrors.aliyun.com
baseurl=https://mirrors.aliyun.com/epel/7/$basearch
failovermethod=priority
enabled=1
gpgcheck=1
gpgkey=https://mirrors.aliyun.com/epel/RPM-GPG-KEY-EPEL-7
EOF

    print_info "CentOS镜像源配置完成"

    # 清理yum缓存
    yum clean all &> /dev/null || true
    print_info "已清理yum缓存"
}

# 修复yum的Python依赖问题
function fix_yum_python() {
    print_info "开始修复yum的Python依赖问题..."

    # 检查当前yum状态
    if yum --version &> /dev/null; then
        print_info "yum工作正常，无需修复"
        return 0
    fi

    print_warn "yum无法正常工作，开始修复..."

    # 方法1: 查找并链接现有的Python2
    if [ -f "/usr/bin/python2" ]; then
        print_info "找到python2，创建符号链接"
        ln -sf /usr/bin/python2 /usr/bin/python
    elif [ -f "/usr/bin/python2.7" ]; then
        print_info "找到python2.7，创建符号链接"
        ln -sf /usr/bin/python2.7 /usr/bin/python
    else
        print_warn "未找到python2，尝试使用国内镜像下载..."

        # 方法2: 使用国内镜像下载python2 rpm包
        print_info "从阿里云镜像下载python2..."

        # 创建临时目录
        TEMP_DIR="/tmp/python_fix_$$"
        mkdir -p "$TEMP_DIR"
        cd "$TEMP_DIR"

        # 使用国内镜像下载python2 rpm包
        print_info "下载python2 rpm包..."
        PYTHON2_RPM_URL="${SELECTED_CENTOS_MIRROR}/7/os/x86_64/Packages/python-2.7.5-90.el7.x86_64.rpm"

        if command -v wget &> /dev/null; then
            wget -q "$PYTHON2_RPM_URL" -O python-2.7.5-90.el7.x86_64.rpm
        elif command -v curl &> /dev/null; then
            curl -s -o python-2.7.5-90.el7.x86_64.rpm "$PYTHON2_RPM_URL"
        else
            print_error "未找到wget或curl，无法下载rpm包"
            return 1
        fi

        # 安装python2
        if [ -f "python-2.7.5-90.el7.x86_64.rpm" ]; then
            print_info "安装python2..."
            rpm -ivh python-2.7.5-90.el7.x86_64.rpm --force --nodeps

            # 创建符号链接
            if [ -f "/usr/bin/python2.7" ]; then
                ln -sf /usr/bin/python2.7 /usr/bin/python
                print_info "python2安装成功"
            else
                print_error "python2安装失败"
                return 1
            fi
        else
            print_error "无法下载python2 rpm包"
            return 1
        fi

        # 清理临时文件
        cd /
        rm -rf "$TEMP_DIR"
    fi

    # 验证修复结果
    if yum --version &> /dev/null; then
        print_info "yum修复成功！"
        return 0
    else
        print_error "yum修复失败"
        return 1
    fi
}

# 安装Python 3.6 (与check_env.sh保持一致)
function install_python36() {
    print_info "开始安装Python $TARGET_PYTHON_VERSION..."

    # 确保yum正常工作
    if ! yum --version &> /dev/null; then
        print_error "yum仍然无法工作，无法安装Python 3.6"
        return 1
    fi

    # 安装Python 3.6和相关工具
    print_info "使用yum安装Python 3.6..."
    yum install -y python36 python36-pip python36-devel

    # 检查安装结果
    if command -v python3.6 &> /dev/null; then
        INSTALLED_VERSION=$(python3.6 --version 2>&1)
        print_info "Python 3.6安装成功: $INSTALLED_VERSION"

        # 创建符号链接
        ln -sf /usr/bin/python3.6 /usr/bin/python3
        ln -sf /usr/bin/pip3.6 /usr/bin/pip3
        print_info "已创建Python 3.6符号链接"

        # 升级pip使用国内源
        if command -v pip3 &> /dev/null; then
            print_info "升级pip使用国内源..."
            pip3 install --upgrade pip -i "$SELECTED_PYPI_SOURCE"
            print_info "pip升级完成"
        fi

        return 0
    else
        print_error "Python 3.6安装失败"
        return 1
    fi
}

# 检查网络连接
function check_network() {
    print_info "检查网络连接..."
    if ping -c 1 ******* &> /dev/null || ping -c 1 *************** &> /dev/null; then
        print_info "网络连接正常"
        return 0
    else
        print_warn "网络连接可能有问题，但继续尝试..."
        return 1
    fi
}

# 主函数
function main() {
    print_info "===== yum修复和Python环境配置脚本开始 ====="
    print_info "目标Python版本: $TARGET_PYTHON_VERSION"
    print_info "使用CentOS镜像: $SELECTED_CENTOS_MIRROR"
    print_info "使用Python源: $SELECTED_PYPI_SOURCE"

    # 检查root权限
    check_root

    # 检查网络连接
    check_network

    # 步骤1: 修复yum的Python依赖
    print_info "步骤1: 修复yum的Python依赖问题"
    if ! fix_yum_python; then
        print_error "yum修复失败，脚本退出"
        exit 1
    fi

    # 步骤2: 配置国内镜像源
    print_info "步骤2: 配置CentOS国内镜像源"
    setup_centos_mirrors

    # 步骤3: 安装Python 3.6
    print_info "步骤3: 安装Python $TARGET_PYTHON_VERSION"
    read -p "是否安装Python $TARGET_PYTHON_VERSION? (y/n): " choice
    if [ "$choice" = "y" ] || [ "$choice" = "Y" ]; then
        if install_python36; then
            print_info "Python $TARGET_PYTHON_VERSION 安装成功"
        else
            print_warn "Python $TARGET_PYTHON_VERSION 安装失败，但yum已修复"
        fi
    else
        print_info "跳过Python 3.6安装"
    fi

    # 完成
    print_info "===== 脚本执行完成 ====="
    print_info "yum已修复并配置国内镜像源"

    # 显示Python版本信息
    if command -v python &> /dev/null; then
        print_info "当前Python版本: $(python --version 2>&1)"
    fi
    if command -v python3 &> /dev/null; then
        print_info "当前Python3版本: $(python3 --version 2>&1)"
    fi
    if command -v python3.6 &> /dev/null; then
        print_info "当前Python3.6版本: $(python3.6 --version 2>&1)"
    fi

    print_info "现在可以正常使用yum命令了"
    print_info "请运行 ./check_env.sh 继续环境配置"
}

# 执行主函数
main
