ffmpeg -i "rtsp://your_stream_url" -c:v libx264 -c:a aac -f flv "output.flv"

ffmpeg -i "rtsp://admin:hnaXMTpl1nk@*************:6060/stream2" -vcodec copy -acodec copy -f flv "rtmp://127.0.0.1:1935/live/0501001305"

ffmpeg -i rtsp://admin:hnaXMTpl1nk@*************:6060/stream2 -c copy -f flv rtmp://localhost/live/stream


rtsp://admin:hnaXMTpl1nk@*************:6060/stream2

rtsp://admin:123456@************:554/video1

ffmpeg -i rtsp://admin:123456@************:554/video1 -codec:v copy -codec:a aac -strict experimental -f hls /home/<USER>/hls/stream.m3u8


ffmpeg -i rtsp://admin:123456@************:554/video1 -c:v copy -c:a aac -strict experimental /home/<USER>/stream.flv



是的，需要将 CentOS 的 yum 源也修改为国内源。以下是完整的解决方案：

1. 备份原有 yum 源

sudo mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup

2. 下载阿里云 CentOS 7 源

sudo curl -o /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-7.repo


3. 清理并重建 yum 缓存

sudo yum clean all
sudo yum makecache

安装 Python 3 和相关工具
sudo yum install -y python3 python3-pip python3-virtualenv

创建新的 Python 3 虚拟环境
python3 -m venv myenv3
source myenv3/bin/activate

配置 pip 国内源（阿里云）

mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
index-url = https://mirrors.aliyun.com/pypi/simple/
trusted-host = mirrors.aliyun.com
EOF


安装所需依赖包
pip3 install --upgrade pip
#pip install flask opencv-python numpy
pip3 install opencv-python-headless flask numpy
pip3 install opencv-python==*********

