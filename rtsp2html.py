'''
Author: Rock
Date: 2025-07-25 12:25:53
LastEditors: Rock
LastEditTime: 2025-07-31 17:31:47
Description: HTTP RTSP Stream Viewer - Improved Version
'''
import cv2
import time
import threading
import queue
import logging
from flask import Flask, Response, render_template_string
import argparse
import os
from typing import Optional, Tuple


class RTSPStream:
    """RTSP流处理器类"""
    
    def __init__(self, rtsp_url: str = "rtsp://admin:123456@************:554/video1", 
                 queue_size: int = 2, 
                 max_retries: int = 10):
        """
        初始化RTSP流处理器
        
        Args:
            rtsp_url: RTSP流地址
            queue_size: 帧队列大小
            max_retries: 最大重试次数
        """
        # 配置日志
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.rtsp_url = rtsp_url
        self.queue_size = queue_size
        self.max_retries = max_retries
        
        # 初始化变量
        self.frame_queue = queue.Queue(maxsize=queue_size)
        self.is_streaming = False
        self.rtsp_thread: Optional[threading.Thread] = None
        self.cap: Optional[cv2.VideoCapture] = None
        
        # 创建空白帧用于连接失败时显示
        self.blank_frame = self._create_blank_frame()
        
    def _create_blank_frame(self) -> bytes:
        """创建空白帧用于连接失败时显示"""
        frame = cv2.zeros((480, 640, 3), dtype=cv2.uint8)
        cv2.putText(frame, 'No Signal', (250, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        ret, buffer = cv2.imencode('.jpg', frame, [
            cv2.IMWRITE_JPEG_QUALITY, 85,
            cv2.IMWRITE_JPEG_OPTIMIZE, 1
        ])
        
        if not ret:
            return b''
        return buffer.tobytes()
    
    def _connect_to_rtsp(self) -> bool:
        """
        尝试连接到RTSP流
        
        Returns:
            bool: 连接是否成功
        """
        # 尝试不同的后端
        backends = [cv2.CAP_FFMPEG, cv2.CAP_GSTREAMER, cv2.CAP_ANY]
        
        for backend in backends:
            try:
                self.logger.info(f"尝试使用后端: {backend}")
                self.cap = cv2.VideoCapture(self.rtsp_url, backend)
                
                # 设置连接参数
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                self.cap.set(cv2.CAP_PROP_FPS, 25)
                self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))
                
                # 测试连接
                ret, test_frame = self.cap.read()
                if ret and test_frame is not None:
                    self.logger.info(f"成功连接RTSP流，使用后端: {backend}")
                    return True
                else:
                    if self.cap:
                        self.cap.release()
                        self.cap = None
            except Exception as e:
                self.logger.error(f"后端 {backend} 连接失败: {e}")
                if self.cap:
                    self.cap.release()
                    self.cap = None
        
        self.logger.error("所有后端都无法连接RTSP流")
        return False
    
    def _reconnect_to_rtsp(self) -> bool:
        """
        重新连接到RTSP流
        
        Returns:
            bool: 重连是否成功
        """
        if self.cap:
            self.cap.release()
            self.cap = None
            
        time.sleep(2)  # 筭短延迟后再重连
        return self._connect_to_rtsp()
    
    def rtsp_reader(self):
        """独立线程读取RTSP流"""
        if not self._connect_to_rtsp():
            return
            
        retry_count = 0
        
        try:
            while self.is_streaming:
                if not self.cap:
                    if not self._reconnect_to_rtsp():
                        time.sleep(1)
                        continue
                
                ret, frame = self.cap.read()
                
                if not ret or frame is None:
                    retry_count += 1
                    self.logger.warning(f"读取帧失败，重试次数: {retry_count}/{self.max_retries}")
                    
                    if retry_count >= self.max_retries:
                        self.logger.error("达到最大重试次数，重新连接...")
                        if not self._reconnect_to_rtsp():
                            time.sleep(1)
                            continue
                        retry_count = 0
                    
                    time.sleep(0.1)
                    continue
                
                retry_count = 0  # 重置重试计数
                
                # 调整帧大小以提高性能
                height, width = frame.shape[:2]
                if width > 1280:
                    scale = 1280 / width
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    frame = cv2.resize(frame, (new_width, new_height))
                
                # 将帧放入队列
                try:
                    if not self.frame_queue.full():
                        self.frame_queue.put(frame, block=False)
                    else:
                        # 如果队列满了，移除旧帧
                        try:
                            self.frame_queue.get_nowait()
                            self.frame_queue.put(frame, block=False)
                        except queue.Empty:
                            pass
                except Exception as e:
                    self.logger.error(f"放入帧队列时出错: {e}")
                    
        except Exception as e:
            self.logger.error(f"RTSP读取线程错误: {e}")
        finally:
            if self.cap:
                self.cap.release()
                self.cap = None
            self.logger.info("RTSP读取线程结束")
    
    def generate_frames(self):
        """生成视频帧"""
        while True:
            try:
                # 从队列获取帧，超时1秒
                frame = self.frame_queue.get(timeout=1.0)
                
                # 编码为JPEG
                ret, buffer = cv2.imencode('.jpg', frame, [
                    cv2.IMWRITE_JPEG_QUALITY, 85,
                    cv2.IMWRITE_JPEG_OPTIMIZE, 1
                ])
                
                if not ret:
                    continue
                
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                
            except queue.Empty:
                # 如果队列为空，发送空白帧
                self.logger.warning("队列为空，发送空白帧")
                if self.blank_frame:
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + self.blank_frame + b'\r\n')
            except Exception as e:
                self.logger.error(f"生成帧错误: {e}")
                time.sleep(0.1)
    
    def start_streaming(self):
        """启动RTSP流读取线程"""
        self.is_streaming = True
        self.rtsp_thread = threading.Thread(target=self.rtsp_reader, daemon=True)
        self.rtsp_thread.start()
        self.logger.info("RTSP流读取线程已启动")
    
    def stop_streaming(self):
        """停止RTSP流读取"""
        self.is_streaming = False
        if self.rtsp_thread and self.rtsp_thread.is_alive():
            self.rtsp_thread.join(timeout=2)
        self.logger.info("RTSP流读取已停止")
    
    def get_status(self) -> dict:
        """获取流状态"""
        return {
            'streaming': self.is_streaming,
            'queue_size': self.frame_queue.qsize(),
            'rtsp_url': self.rtsp_url.replace('123456', '***')  # 隐藏密码
        }


def create_app(rtsp_url: str = "rtsp://admin:123456@************:554/video1") -> Tuple[Flask, RTSPStream]:
    """
    创建Flask应用和RTSP流处理器
    
    Args:
        rtsp_url: RTSP流地址
        
    Returns:
        Tuple[Flask, RTSPStream]: Flask应用和RTSP流处理器实例
    """
    app = Flask(__name__)
    stream = RTSPStream(rtsp_url=rtsp_url)
    
    @app.route('/video')
    def video():
        return Response(stream.generate_frames(),
                        mimetype='multipart/x-mixed-replace; boundary=frame')
    
    @app.route('/')
    def index():
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>RTSP Live Stream</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0f0f0; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #333; text-align: center; }
                .video-container { text-align: center; margin: 20px 0; }
                .video-stream { max-width: 100%; height: auto; border: 2px solid #ddd; border-radius: 5px; }
                .info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .status { text-align: center; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎥 RTSP Live Stream Viewer</h1>
                <div class="info">
                    <strong>流地址:</strong> rtsp://admin:***@************:554/video1<br>
                    <strong>状态:</strong> <span id="status">连接中...</span>
                </div>
                <div class="video-container">
                    <img src="/video" class="video-stream" alt="Live Stream"
                         onload="updateStatus('已连接')"
                         onerror="updateStatus('连接失败')">
                </div>
            </div>

            <script>
                function updateStatus(status) {
                    document.getElementById('status').textContent = status;
                }

                // 定期检查连接状态
                setInterval(function() {
                    var img = document.querySelector('.video-stream');
                    if (img.complete && img.naturalHeight !== 0) {
                        updateStatus('正常播放');
                    } else {
                        updateStatus('连接中断');
                    }
                }, 5000);
            </script>
        </body>
        </html>
        '''
    
    @app.route('/status')
    def status():
        """返回流状态"""
        return stream.get_status()
    
    return app, stream


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='RTSP to HTTP Stream Viewer')
    parser.add_argument('--rtsp-url', type=str, 
                        default="rtsp://admin:123456@************:554/video1",
                        help='RTSP流地址')
    parser.add_argument('--host', type=str, default='0.0.0.0',
                        help='主机地址')
    parser.add_argument('--port', type=int, default=5000,
                        help='端口号')
    
    args = parser.parse_args()
    
    # 创建应用和流处理器
    app, stream = create_app(args.rtsp_url)
    
    try:
        # 启动RTSP流读取
        stream.start_streaming()
        
        logger.info(f"启动Flask服务器在 {args.host}:{args.port}...")
        app.run(host=args.host, port=args.port, debug=False, threaded=True)
    except KeyboardInterrupt:
        logger.info("接收到中断信号")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
    finally:
        stream.stop_streaming()
        logger.info("服务器关闭")


if __name__ == '__main__':
    main()
