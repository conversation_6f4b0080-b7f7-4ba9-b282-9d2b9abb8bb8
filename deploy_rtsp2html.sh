#!/bin/bash

# RTSP2HTML部署脚本
# 适用于CentOS 7或更高版本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color# 修改 install_python_dependencies 函数
install_python_dependencies() {
    print_info "安装Python依赖..."
    
    # 检查 OpenCV 是否已安装
    if python3 -c "import cv2" &> /dev/null; then
        print_info "OpenCV 已经安装，跳过安装"
    else
        print_info "尝试安装 opencv-python..."
        # 尝试多种安装方法
        pip3 install opencv-python 2>/dev/null || \
        pip3 install --only-binary=all opencv-python 2>/dev/null || \
        print_warning "无法通过pip安装OpenCV，确保已通过系统包管理器安装"
    fi
    
    # 检查 Flask 是否已安装
    if python3 -c "import flask" &> /dev/null; then
        print_info "Flask 已经安装，跳过安装"
    else
        print_info "安装 Flask..."
        pip3 install flask >> "$LOG_FILE" 2>&1
    fi
    
    # 验证安装
    if python3 -c "import cv2" &> /dev/null && python3 -c "import flask" &> /dev/null; then
        print_success "Python依赖安装完成"
        log_message "Python依赖安装完成"
    elif python3 -c "import flask" &> /dev/null; then
        print_warning "Flask 安装完成，OpenCV 可能已通过系统包安装"
        log_message "Flask 安装完成，OpenCV 可能已通过系统包安装"
    else
        print_error "Python依赖安装失败"
        log_message "Python依赖安装失败"
        exit 1
    fi
}

# 日志文件
LOG_FILE="/var/log/rtsp2html_deploy.log"

# 应用相关变量
APP_NAME="rtsp2html"
APP_DIR="/opt/rtsp2html"
SERVICE_NAME="rtsp2html.service"
USER="rtsp2html"
GROUP="rtsp2html"

# RTSP URL默认值
DEFAULT_RTSP_URL="rtsp://admin:123456@************:554/video1"

# 配置文件路径
CONFIG_FILE="/etc/rtsp2html.conf"

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 记录日志
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "此脚本必须以root权限运行"
        exit 1
    fi
}

# 检查系统版本
check_os_version() {
    print_info "检查系统版本..."
    if [[ -f /etc/redhat-release ]]; then
        if grep -q "CentOS Linux release 7\|CentOS Linux release 8\|CentOS Stream release 8\|CentOS Stream release 9" /etc/redhat-release; then
            print_success "检测到兼容的CentOS版本: $(cat /etc/redhat-release)"
            log_message "检测到兼容的CentOS版本: $(cat /etc/redhat-release)"
        else
            print_error "不支持的操作系统版本。此脚本仅适用于CentOS 7或更高版本。"
            log_message "不支持的操作系统版本: $(cat /etc/redhat-release)"
            exit 1
        fi
    else
        print_error "无法检测操作系统版本。此脚本仅适用于CentOS 7或更高版本。"
        log_message "无法检测操作系统版本"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    # 检查yum是否可用
    if ! command -v yum &> /dev/null; then
        print_error "未找到yum包管理器"
        log_message "未找到yum包管理器"
        exit 1
    fi
    
    # 检查systemctl是否可用
    if ! command -v systemctl &> /dev/null; then
        print_error "未找到systemctl命令"
        log_message "未找到systemctl命令"
        exit 1
    fi
    
    print_success "系统依赖检查完成"
    log_message "系统依赖检查完成"
}

# 创建配置文件
create_config_file() {
    print_info "创建配置文件..."
    
    # 如果配置文件不存在，则创建默认配置文件
    if [[ ! -f "$CONFIG_FILE" ]]; then
        cat > "$CONFIG_FILE" << EOF
# RTSP2HTML配置文件
RTSP_URL="$DEFAULT_RTSP_URL"
HOST="0.0.0.0"
PORT="5000"
EOF
        print_info "已创建默认配置文件: $CONFIG_FILE"
        log_message "已创建默认配置文件: $CONFIG_FILE"
    else
        print_info "配置文件已存在: $CONFIG_FILE"
        log_message "配置文件已存在: $CONFIG_FILE"
    fi
    
    # 设置配置文件权限
    chmod 644 "$CONFIG_FILE"
    chown root:root "$CONFIG_FILE"
}

# 安装必要的软件包
install_packages() {
    print_info "安装必要的软件包..."
    yum update -y >> "$LOG_FILE" 2>&1
    
    # 安装EPEL仓库（如果需要）
    if ! rpm -q epel-release &> /dev/null; then
        print_info "安装EPEL仓库..."
        yum install -y epel-release >> "$LOG_FILE" 2>&1
    fi
    
    # 安装Python 3和pip
    print_info "安装Python 3和pip..."
    yum install -y python3 python3-pip >> "$LOG_FILE" 2>&1
    
    # 验证安装
    if command -v python3 &> /dev/null && command -v pip3 &> /dev/null; then
        print_success "Python 3和pip安装完成"
        log_message "Python 3和pip安装完成"
    else
        print_error "Python 3或pip安装失败"
        log_message "Python 3或pip安装失败"
        exit 1
    fi
}

# 安装Python依赖 - 修改后的智能版本
install_python_dependencies() {
    print_info "安装Python依赖..."
    
    # 检查 OpenCV 是否已安装
    if python3 -c "import cv2" &> /dev/null; then
        print_info "OpenCV 已经安装，跳过安装"
    else
        print_info "安装 opencv-python..."
        pip3 install opencv-python >> "$LOG_FILE" 2>&1
    fi
    
    # 检查 Flask 是否已安装
    if python3 -c "import flask" &> /dev/null; then
        print_info "Flask 已经安装，跳过安装"
    else
        print_info "安装 Flask..."
        pip3 install flask >> "$LOG_FILE" 2>&1
    fi
    
    # 验证安装
    if python3 -c "import cv2" &> /dev/null && python3 -c "import flask" &> /dev/null; then
        print_success "Python依赖安装完成"
        log_message "Python依赖安装完成"
    else
        print_error "Python依赖安装失败"
        log_message "Python依赖安装失败"
        exit 1
    fi
}

# 创建应用目录和用户
setup_app_environment() {
    print_info "设置应用环境..."
    
    # 创建应用目录
    if [[ ! -d "$APP_DIR" ]]; then
        mkdir -p "$APP_DIR"
        print_info "创建应用目录: $APP_DIR"
        log_message "创建应用目录: $APP_DIR"
    fi
    
    # 创建应用用户和组
    if ! getent group "$GROUP" > /dev/null 2>&1; then
        groupadd "$GROUP"
        print_info "创建用户组: $GROUP"
        log_message "创建用户组: $GROUP"
    fi
    
    if ! getent passwd "$USER" > /dev/null 2>&1; then
        useradd -r -g "$GROUP" -d "$APP_DIR" -s /sbin/nologin "$USER"
        print_info "创建用户: $USER"
        log_message "创建用户: $USER"
    fi
    
    # 设置目录权限
    chown -R "$USER":"$GROUP" "$APP_DIR"
    chmod 755 "$APP_DIR"
    
    print_success "应用环境设置完成"
    log_message "应用环境设置完成"
}

# 复制应用文件
copy_app_files() {
    print_info "复制应用文件..."
    
    # 检查rtsp2html.py是否存在
    if [[ ! -f "rtsp2html.py" ]]; then
        print_error "未找到rtsp2html.py文件，请确保该文件在当前目录中"
        log_message "未找到rtsp2html.py文件"
        exit 1
    fi
    
    # 复制应用文件
    cp rtsp2html.py "$APP_DIR/"
    
    # 设置文件权限
    chown "$USER":"$GROUP" "$APP_DIR/rtsp2html.py"
    chmod 644 "$APP_DIR/rtsp2html.py"
    
    print_success "应用文件复制完成"
    log_message "应用文件复制完成"
}

# 创建systemd服务文件
create_service_file() {
    print_info "创建systemd服务文件..."
    
    # 读取配置
    load_config
    
    # 获取Python 3路径
    PYTHON_PATH=$(which python3)
    
    # 创建服务文件
    cat > "/etc/systemd/system/$SERVICE_NAME" << EOF
[Unit]
Description=RTSP to HTML Stream Viewer
After=network.target

[Service]
Type=simple
User=$USER
Group=$GROUP
WorkingDirectory=$APP_DIR
ExecStart=$PYTHON_PATH $APP_DIR/rtsp2html.py --rtsp-url=$RTSP_URL --host=$HOST --port=$PORT
Restart=always
RestartSec=10
Environment=PYTHONPATH=$APP_DIR

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    print_success "systemd服务文件创建完成"
    log_message "systemd服务文件创建完成"
}

# 启动服务
start_service() {
    print_info "启动$APP_NAME服务..."
    
    systemctl start "$SERVICE_NAME"
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        print_success "$APP_NAME服务启动成功"
        log_message "$APP_NAME服务启动成功"
    else
        print_error "$APP_NAME服务启动失败"
        log_message "$APP_NAME服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_service() {
    print_info "停止$APP_NAME服务..."
    
    systemctl stop "$SERVICE_NAME"
    
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        print_success "$APP_NAME服务已停止"
        log_message "$APP_NAME服务已停止"
    else
        print_error "$APP_NAME服务停止失败"
        log_message "$APP_NAME服务停止失败"
    fi
}

# 重启服务
restart_service() {
    print_info "重启$APP_NAME服务..."
    
    systemctl restart "$SERVICE_NAME"
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        print_success "$APP_NAME服务重启成功"
        log_message "$APP_NAME服务重启成功"
    else
        print_error "$APP_NAME服务重启失败"
        log_message "$APP_NAME服务重启失败"
        exit 1
    fi
}

# 启用开机自启动
enable_autostart() {
    print_info "设置$APP_NAME服务开机自启动..."
    
    systemctl enable "$SERVICE_NAME" >> "$LOG_FILE" 2>&1
    
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        print_success "$APP_NAME服务已设置为开机自启动"
        log_message "$APP_NAME服务已设置为开机自启动"
    else
        print_error "$APP_NAME服务开机自启动设置失败"
        log_message "$APP_NAME服务开机自启动设置失败"
    fi
}

# 禁用开机自启动
disable_autostart() {
    print_info "禁用$APP_NAME服务开机自启动..."
    
    systemctl disable "$SERVICE_NAME" >> "$LOG_FILE" 2>&1
    
    if ! systemctl is-enabled --quiet "$SERVICE_NAME"; then
        print_success "$APP_NAME服务已禁用开机自启动"
        log_message "$APP_NAME服务已禁用开机自启动"
    else
        print_error "$APP_NAME服务开机自启动禁用失败"
        log_message "$APP_NAME服务开机自启动禁用失败"
    fi
}

# 验证RTSP URL格式
validate_rtsp_url() {
    local url=$1
    if [[ $url =~ ^rtsp://[a-zA-Z0-9._-]+:[0-9]+/[a-zA-Z0-9._/-]*$ ]] || \
       [[ $url =~ ^rtsp://[a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+:[0-9]+/[a-zA-Z0-9._/-]*$ ]]; then
        return 0
    else
        return 1
    fi
}

# 读取配置文件
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        # 读取配置文件
        source "$CONFIG_FILE"
        print_info "已加载配置文件: $CONFIG_FILE"
        log_message "已加载配置文件: $CONFIG_FILE"
        
        # 验证RTSP URL格式
        if ! validate_rtsp_url "$RTSP_URL"; then
            print_warning "RTSP URL格式可能不正确，使用默认URL"
            log_message "RTSP URL格式可能不正确，使用默认URL"
            RTSP_URL="$DEFAULT_RTSP_URL"
        fi
    else
        # 使用默认值
        RTSP_URL="$DEFAULT_RTSP_URL"
        HOST="0.0.0.0"
        PORT="5000"
        
        print_warning "配置文件不存在，使用默认配置"
        log_message "配置文件不存在，使用默认配置"
    fi
}

# 检查服务状态
check_status() {
    print_info "检查$APP_NAME服务状态..."
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        print_success "$APP_NAME服务正在运行"
        log_message "$APP_NAME服务正在运行"
    else
        print_warning "$APP_NAME服务未运行"
        log_message "$APP_NAME服务未运行"
    fi
    
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        print_success "$APP_NAME服务已设置为开机自启动"
        log_message "$APP_NAME服务已设置为开机自启动"
    else
        print_warning "$APP_NAME服务未设置为开机自启动"
        log_message "$APP_NAME服务未设置为开机自启动"
    fi
}

# 检查防火墙设置
check_firewall() {
    print_info "检查防火墙设置..."
    
    # 检查firewalld是否运行
    if systemctl is-active --quiet firewalld; then
        # 读取配置
        load_config
        
        # 检查端口是否在防火墙中开放
        if firewall-cmd --list-ports | grep -q "$PORT/tcp"; then
            print_success "端口$PORT在防火墙中已开放"
            log_message "端口$PORT在防火墙中已开放"
        else
            print_warning "端口$PORT在防火墙中未开放"
            log_message "端口$PORT在防火墙中未开放"
            print_info "建议运行以下命令开放端口:"
            print_info "  sudo firewall-cmd --permanent --add-port=$PORT/tcp"
            print_info "  sudo firewall-cmd --reload"
        fi
    elif command -v iptables &> /dev/null; then
        # 检查iptables规则
        if iptables -L -n | grep -q "ACCEPT.*tcp.*dpt:$PORT"; then
            print_success "端口$PORT在iptables中已开放"
            log_message "端口$PORT在iptables中已开放"
        else
            print_warning "端口$PORT在iptables中未开放"
            log_message "端口$PORT在iptables中未开放"
        fi
    else
        print_warning "未检测到防火墙服务"
        log_message "未检测到防火墙服务"
    fi
}

# 健康检查
health_check() {
    print_info "执行健康检查..."
    
    # 检查服务是否运行
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        print_error "$APP_NAME服务未运行"
        log_message "$APP_NAME服务未运行，健康检查失败"
        return 1
    fi
    
    # 读取配置
    load_config
    
    # 检查端口是否监听
    if netstat -tlnp | grep -q ":$PORT "; then
        print_success "端口$PORT正在监听"
        log_message "端口$PORT正在监听"
    else
        print_warning "端口$PORT未监听"
        log_message "端口$PORT未监听"
    fi
    
    # 检查Web服务是否响应
    if curl -s -f -m 5 "http://localhost:$PORT" > /dev/null; then
        print_success "Web服务响应正常"
        log_message "Web服务响应正常"
    else
        print_warning "Web服务无响应"
        log_message "Web服务无响应"
    fi
    
    # 检查防火墙设置
    check_firewall
    
    print_success "健康检查完成"
    log_message "健康检查完成"
}

# 显示使用帮助
show_help() {
    echo "RTSP2HTML部署脚本使用说明:"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  install     安装并启动RTSP2HTML服务"
    echo "  start       启动RTSP2HTML服务"
    echo "  stop        停止RTSP2HTML服务"
    echo "  restart     重启RTSP2HTML服务"
    echo "  status      检查RTSP2HTML服务状态"
    echo "  enable      设置RTSP2HTML服务开机自启动"
    echo "  disable     禁用RTSP2HTML服务开机自启动"
    echo "  health      执行RTSP2HTML服务健康检查"
    echo "  uninstall   卸载RTSP2HTML服务"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 install   安装并启动服务"
    echo "  $0 start     启动服务"
}

# 卸载服务
uninstall_service() {
    print_info "卸载$APP_NAME服务..."
    
    # 停止服务
    systemctl stop "$SERVICE_NAME" 2>/dev/null
    
    # 禁用开机自启动
    systemctl disable "$SERVICE_NAME" 2>/dev/null
    
    # 删除服务文件
    rm -f "/etc/systemd/system/$SERVICE_NAME"
    
    # 删除应用目录
    rm -rf "$APP_DIR"
    
    # 删除用户和组
    userdel "$USER" 2>/dev/null
    groupdel "$GROUP" 2>/dev/null
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    print_success "$APP_NAME服务卸载完成"
    log_message "$APP_NAME服务卸载完成"
}

# 主函数
main() {
    # 检查是否以root权限运行
    check_root
    
    # 检查系统版本
    check_os_version
    
    # 检查依赖
    check_dependencies
    
    # 根据参数执行相应操作
    case "$1" in
        install)
            print_info "开始安装$APP_NAME服务..."
            log_message "开始安装$APP_NAME服务"
            
            # 安装必要的软件包
            install_packages
            
            # 安装Python依赖
            install_python_dependencies
            
            # 设置应用环境
            setup_app_environment
            
            # 复制应用文件
            copy_app_files
            
            # 创建systemd服务文件
            create_service_file
            
            # 启动服务
            start_service
            
            # 设置开机自启动
            enable_autostart
            
            print_success "$APP_NAME服务安装完成！"
            log_message "$APP_NAME服务安装完成"
            print_info "访问地址: http://服务器IP:5000"
            
            # 执行健康检查
            health_check
            ;;
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            check_status
            ;;
        enable)
            enable_autostart
            ;;
        disable)
            disable_autostart
            ;;
        health)
            health_check
            ;;
        uninstall)
            uninstall_service
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "无效的参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 如果直接运行脚本，则执行main函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi