import re
import requests

# 获取网页源码
url = "https://bbs.shuiguobang.com/forum.php?mod=forumdisplay&fid=1461275&filter=author&orderby=dateline"
response = requests.get(url)
html_content = response.text

# 正则表达式匹配
# pattern = r'normalthread_\d+">[\s\S]*?<a class="s xst">(.*?)</a>'
pattern = r'normalthread_\d+.*?[\s\S]*?previewThread\(\'(.*?)\'[\s\S]*?class=\"s xst\">(.*?)<\/a>'


# 查找所有匹配项
matches = re.findall(pattern, html_content)

# 输出匹配结果
for match in matches:
    print(match)
