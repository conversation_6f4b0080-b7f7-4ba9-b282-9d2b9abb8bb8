'''
RTSP流转发器 - 实时播放版本
功能：
1. 将RTSP流转发为HTTP流
2. 提供Web界面实时播放
3. 不使用缓存，实时播放

依赖安装：
pip install opencv-python flask

使用方法：
python rtsp_stream_viewer.py

访问地址：
http://localhost:5000
'''

import cv2
import threading
import queue
import logging
from flask import Flask, Response
import argparse

# 配置日志
import os
# 设置FFmpeg日志级别为error，减少输出
os.environ['FFREPORT'] = 'file=/dev/null:level=32'
# 配置Python日志
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)
# 只显示错误和严重警告
logger.setLevel(logging.WARNING)

app = Flask(__name__)

# 全局变量
frame_queue = queue.Queue(maxsize=2)
rtsp_url = "rtsp://admin:123456@************:554/video1"
is_streaming = False


def rtsp_reader():
    """独立线程读取RTSP流"""
    global is_streaming

    # 尝试连接到RTSP流
    cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
    
    # 设置连接参数
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
    cap.set(cv2.CAP_PROP_FPS, 25)
    
    if not cap.isOpened():
        logger.error("无法打开RTSP流")
        return
    
    logger.info("成功连接RTSP流")
    
    try:
        while is_streaming:
            ret, frame = cap.read()
            
            if not ret or frame is None:
                # 减少警告频率，每10秒最多显示一次
                import time
                current_time = time.time()
                if not hasattr(rtsp_reader, 'last_warning_time') or current_time - rtsp_reader.last_warning_time > 10:
                    logger.warning("读取帧失败")
                    rtsp_reader.last_warning_time = current_time
                continue
            
            # 调整帧大小以提高性能
            height, width = frame.shape[:2]
            if width > 1280:
                scale = 1280 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                frame = cv2.resize(frame, (new_width, new_height))
            
            # 将帧放入队列
            try:
                if not frame_queue.full():
                    frame_queue.put(frame, block=False)
                else:
                    # 如果队列满了，移除旧帧
                    try:
                        frame_queue.get_nowait()
                        frame_queue.put(frame, block=False)
                    except queue.Empty:
                        pass
            except Exception as e:
                logger.error(f"放入帧队列时出错: {e}")
                
    except Exception as e:
        logger.error(f"RTSP读取线程错误: {e}")
    finally:
        cap.release()
        logger.info("RTSP读取线程结束")


def generate_frames():
    """生成视频帧"""
    while True:
        try:
            # 从队列获取帧，超时1秒
            frame = frame_queue.get(timeout=1.0)
            
            # 编码为JPEG
            ret, buffer = cv2.imencode('.jpg', frame, [
                cv2.IMWRITE_JPEG_QUALITY, 85
            ])
            
            if not ret:
                continue
            
            frame_bytes = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            
        except queue.Empty:
            # 队列为空时继续等待
            continue
        except Exception as e:
            logger.error(f"生成帧错误: {e}")
            continue


@app.route('/video')
def video():
    return Response(generate_frames(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')


@app.route('/')
def index():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>RTSP Live Stream</title>
        <meta charset="utf-8">
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background-color: #f0f0f0; 
            }
            .container { 
                max-width: 1200px; 
                margin: 0 auto; 
                background: white; 
                padding: 20px; 
                border-radius: 10px; 
                box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            }
            h1 { 
                color: #333; 
                text-align: center; 
            }
            .video-container { 
                text-align: center; 
                margin: 20px 0; 
            }
            .video-stream { 
                max-width: 100%; 
                height: auto; 
                border: 2px solid #ddd; 
                border-radius: 5px; 
            }
            .embed-info {
                text-align: center;
                margin-top: 30px;
                padding: 15px;
                background-color: #f8f8f8;
                border-radius: 5px;
            }
            .code-block {
                background-color: #eee;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                word-break: break-all;
                max-width: 800px;
                margin: 0 auto;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>仓库实时画面</h1>
            <div class="video-container">
                <img src="/video" class="video-stream" alt="Live Stream">
            </div>
            <div class="embed-info">
                <h3>嵌入到其他页面</h3>
                <p>您可以使用以下代码将视频流嵌入到其他HTML页面中：</p>
                <div class="code-block">&lt;iframe src=&quot;http://localhost:5000/live&quot; width=&quot;800&quot; height=&quot;600&quot; frameborder=&quot;0&quot; allowfullscreen&gt;&lt;/iframe&gt;</div>
            </div>
        </div>
    </body>
    </html>
    '''


@app.route('/live')
def embed():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>RTSP Live Stream (Embed)</title>
        <meta charset="utf-8">
        <style>
            body { 
                margin: 0; 
                padding: 0; 
                overflow: hidden;
            }
            .video-container { 
                width: 100%; 
                height: 100%; 
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .video-stream { 
                max-width: 100%; 
                max-height: 100%; 
            }
        </style>
    </head>
    <body>
        <div class="video-container">
            <img src="/video" class="video-stream" alt="Live Stream">
        </div>
    </body>
    </html>
    '''

def main():
    """主函数"""
    global is_streaming
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='RTSP to HTTP Stream Viewer')
    parser.add_argument('--rtsp-url', type=str, 
                        default="rtsp://admin:123456@************:554/video1",
                        help='RTSP流地址')
    parser.add_argument('--host', type=str, default='0.0.0.0',
                        help='主机地址')
    parser.add_argument('--port', type=int, default=5000,
                        help='端口号')
    parser.add_argument('--quiet', action='store_true',
                        help='静默模式，减少控制台输出')
    
    args = parser.parse_args()
    
    # 更新RTSP URL
    global rtsp_url
    rtsp_url = args.rtsp_url
    
    # 如果是静默模式，进一步降低日志级别
    if args.quiet:
        logger.setLevel(logging.ERROR)
    
    try:
        # 启动RTSP读取线程
        is_streaming = True
        rtsp_thread = threading.Thread(target=rtsp_reader, daemon=True)
        rtsp_thread.start()
        
        if not args.quiet:
            print(f"启动Flask服务器在 {args.host}:{args.port}...")
            print("访问地址: http://localhost:{args.port}")
            print("嵌入地址: http://localhost:{args.port}/live")
        
        # 关闭Flask的默认日志
        import logging as flask_logging
        werkzeug_logger = flask_logging.getLogger('werkzeug')
        werkzeug_logger.setLevel(flask_logging.ERROR)
        
        app.run(host=args.host, port=args.port, debug=False, threaded=True)
    except KeyboardInterrupt:
        if not args.quiet:
            print("接收到中断信号")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
    finally:
        is_streaming = False
        if not args.quiet:
            print("服务器关闭")


if __name__ == '__main__':
    main()