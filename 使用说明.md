# CentOS 7 Python环境修复使用说明

## 快速开始

如果您遇到了 `yum: /usr/bin/python: bad interpreter: No such file or directory` 错误，请按以下步骤操作：

### 第一步：运行修复脚本
```bash
# 给脚本添加执行权限
chmod +x fix_yum.sh

# 以root用户运行修复脚本
./fix_yum.sh
```

### 第二步：测试修复结果（可选）
```bash
# 给测试脚本添加执行权限
chmod +x test_fix.sh

# 运行测试脚本
./test_fix.sh
```

### 第三步：运行环境检测
```bash
# 给环境检测脚本添加执行权限
chmod +x check_env.sh

# 运行环境检测脚本
./check_env.sh
```

## 脚本功能说明

### fix_yum.sh - 主修复脚本
**功能：**
- 修复yum的Python依赖问题
- 配置CentOS国内镜像源（阿里云）
- 安装Python 3.6.8
- 配置pip使用国内源

**特点：**
- 全程使用国内镜像，速度快
- 自动备份原始配置
- 与check_env.sh版本保持一致
- 支持网络检测和错误处理

### test_fix.sh - 测试脚本
**功能：**
- 测试yum是否正常工作
- 检查Python环境（Python 2/3/3.6）
- 验证pip功能
- 测试网络连接
- 检查yum源配置

### check_env.sh - 环境检测脚本
**功能：**
- 检测Python版本
- 创建虚拟环境
- 安装项目依赖包
- 配置开发环境

## 常见使用场景

### 场景1：全新CentOS 7系统
```bash
# 1. 修复基础环境
./fix_yum.sh

# 2. 配置Python开发环境
./check_env.sh
```

### 场景2：yum损坏的系统
```bash
# 1. 修复yum
./fix_yum.sh

# 2. 测试修复结果
./test_fix.sh

# 3. 如果测试通过，继续环境配置
./check_env.sh
```

### 场景3：只需要修复yum
```bash
# 运行修复脚本，选择不安装Python 3.6
./fix_yum.sh
# 当询问是否安装Python 3.6时，输入 n
```

## 镜像源说明

### CentOS镜像源
- **主源**: 阿里云 (https://mirrors.aliyun.com/centos)
- **备选**: 清华大学、中科大、兰州大学

### Python镜像源
- **主源**: 阿里云 (https://mirrors.aliyun.com/pypi/simple/)
- **备选**: 豆瓣、清华大学

## 故障排除

### 问题1：网络连接失败
```bash
# 测试网络连接
ping mirrors.aliyun.com
ping 8.8.8.8

# 如果网络有问题，检查防火墙和DNS设置
```

### 问题2：权限不足
```bash
# 确保以root用户运行
sudo ./fix_yum.sh
# 或者
su -
./fix_yum.sh
```

### 问题3：脚本执行权限
```bash
# 给所有脚本添加执行权限
chmod +x *.sh
```

### 问题4：rpm包下载失败
```bash
# 手动测试下载
wget https://mirrors.aliyun.com/centos/7/os/x86_64/Packages/python-2.7.5-90.el7.x86_64.rpm

# 如果下载失败，尝试其他镜像源
```

## 验证修复结果

修复完成后，以下命令应该都能正常工作：

```bash
# 基础命令
yum --version
python --version
python3 --version
pip3 --version

# 测试yum功能
yum list installed | head -5

# 测试pip功能
pip3 list

# 测试Python功能
python3 -c "print('Hello, Python 3!')"
```

## 注意事项

1. **必须以root用户运行修复脚本**
2. **确保网络连接正常**
3. **不要在脚本运行过程中中断**
4. **脚本会自动备份重要配置文件**
5. **适用于CentOS 7系统**

## 技术支持

如果遇到问题，请提供以下信息：
- 操作系统版本：`cat /etc/redhat-release`
- 错误信息的完整输出
- 网络连接状态：`ping mirrors.aliyun.com`
- 当前Python环境：`ls -la /usr/bin/python*`

## 相关文件

- `fix_yum.sh` - 主修复脚本
- `test_fix.sh` - 测试脚本  
- `check_env.sh` - 环境检测脚本
- `README_yum_fix.md` - 详细技术文档
- `使用说明.md` - 本文档
