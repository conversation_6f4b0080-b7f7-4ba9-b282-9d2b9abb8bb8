import requests


def main(sql: str) -> dict:
    # 定义API的URL
    url = "http://172.16.113.16:5000/execute"
    # 构造请求体
    payload = {
        # "sql": sql
        "sql": "SELECT \n  DATE_FORMAT(flight_date, '%Y-%m-%d') AS MONTH,\n    RPK / ASK AS passenger_load_factor,\n    passenger_revenue / pax_num AS average_ticket_price\nFROM \n    vw_flight_revenue_cost\nWHERE \n    flight_date BETWEEN '2025-01-01' AND '2025-12-31'\nGROUP BY \n    DATE(flight_date)\nORDER BY \n    passenger_load_factor DESC;"
    }
    # 发送POST请求
    try:
        response = requests.post(url, json=payload)
        # 检查响应状态码
        if response.status_code == 200:
            # 解析响应数据
            result = response.json()
            # 检查是否包含 xAxis 和 yAxis
            if "xAxis" in result and "yAxis" in result:
                return {
                    "xAxis": result["xAxis"],
                    "yAxis": result["yAxis"]
                }
            else:
                return {
                    "result": "响应数据中未找到 xAxis 或 yAxis"
                }
        else:
            return {
                "result": f"请求失败，状态码：{response.status_code}, {response.json()}"
            }
    except requests.exceptions.RequestException as e:
        return {
            "result": f"请求异常：{e}"
        }


# 示例调用
if __name__ == "__main__":
    sql_query = "SELECT MONTH, average_ticket_price FROM your_table"
    result = main(sql_query)
    print(result)
