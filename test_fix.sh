#!/bin/bash

# 测试修复结果的脚本
# 验证yum和Python环境是否正常

# 打印函数，带颜色
function print_info() {
    echo -e "\033[32m[INFO] $1\033[0m"
}

function print_warn() {
    echo -e "\033[33m[WARN] $1\033[0m"
}

function print_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

function print_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

# 测试yum功能
function test_yum() {
    print_info "测试yum功能..."
    
    if yum --version &> /dev/null; then
        print_success "yum工作正常"
        yum --version
        return 0
    else
        print_error "yum仍然无法工作"
        return 1
    fi
}

# 测试Python环境
function test_python() {
    print_info "测试Python环境..."
    
    # 测试Python 2
    if command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version 2>&1)
        print_success "Python 2: $PYTHON_VERSION"
    else
        print_warn "未找到Python 2"
    fi
    
    # 测试Python 3
    if command -v python3 &> /dev/null; then
        PYTHON3_VERSION=$(python3 --version 2>&1)
        print_success "Python 3: $PYTHON3_VERSION"
    else
        print_warn "未找到Python 3"
    fi
    
    # 测试Python 3.6
    if command -v python3.6 &> /dev/null; then
        PYTHON36_VERSION=$(python3.6 --version 2>&1)
        print_success "Python 3.6: $PYTHON36_VERSION"
    else
        print_warn "未找到Python 3.6"
    fi
}

# 测试pip功能
function test_pip() {
    print_info "测试pip功能..."
    
    if command -v pip3 &> /dev/null; then
        PIP_VERSION=$(pip3 --version 2>&1)
        print_success "pip3: $PIP_VERSION"
        
        # 测试pip源
        print_info "测试pip源连接..."
        if pip3 search requests &> /dev/null || pip3 list &> /dev/null; then
            print_success "pip源连接正常"
        else
            print_warn "pip源可能有问题，但pip本身工作正常"
        fi
    else
        print_warn "未找到pip3"
    fi
}

# 测试网络连接
function test_network() {
    print_info "测试网络连接..."
    
    if ping -c 1 mirrors.aliyun.com &> /dev/null; then
        print_success "阿里云镜像连接正常"
    else
        print_warn "阿里云镜像连接可能有问题"
    fi
    
    if ping -c 1 ******* &> /dev/null; then
        print_success "外网连接正常"
    else
        print_warn "外网连接可能有问题"
    fi
}

# 测试yum源
function test_yum_repos() {
    print_info "测试yum源配置..."
    
    if [ -f "/etc/yum.repos.d/CentOS-Base.repo" ]; then
        print_success "CentOS-Base.repo 存在"
        
        # 检查是否包含阿里云镜像
        if grep -q "mirrors.aliyun.com" /etc/yum.repos.d/CentOS-Base.repo; then
            print_success "已配置阿里云镜像源"
        else
            print_warn "未检测到阿里云镜像源配置"
        fi
    else
        print_warn "CentOS-Base.repo 不存在"
    fi
    
    if [ -f "/etc/yum.repos.d/epel.repo" ]; then
        print_success "EPEL源已配置"
    else
        print_warn "EPEL源未配置"
    fi
}

# 主函数
function main() {
    print_info "===== 修复结果测试开始 ====="
    
    # 测试各项功能
    test_yum
    echo
    
    test_python
    echo
    
    test_pip
    echo
    
    test_network
    echo
    
    test_yum_repos
    echo
    
    print_info "===== 测试完成 ====="
    print_info "如果所有测试都通过，可以运行 ./check_env.sh 继续环境配置"
}

# 执行主函数
main
