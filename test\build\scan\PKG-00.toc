('D:\\develop\\python\\test\\build\\scan\\PKG-00.pkg',
 {'BINARY': 1,
  'DATA': 1,
  'EXECUTABLE': 1,
  'EXTENSION': 1,
  'PYMODULE': 1,
  'PYSOURCE': 1,
  'PYZ': 0},
 [('PYZ-00.pyz', 'D:\\develop\\python\\test\\build\\scan\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'D:\\develop\\python\\test\\build\\scan\\localpycos\\struct.pyo',
   'PYMODULE'),
  ('pyimod01_os_path',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyimod01_os_path.pyc',
   'PYMODULE'),
  ('pyimod02_archive',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyimod02_archive.pyc',
   'PYMODULE'),
  ('pyimod03_importers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyimod03_importers.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_certifi',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_certifi.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\loader\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('scan', 'D:\\develop\\python\\test\\scan.py', 'PYSOURCE'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('python38.dll', 'c:\\program files\\python\\python38.dll', 'BINARY'),
  ('VCRUNTIME140.dll', 'c:\\program files\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseGit\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('pywintypes38.dll',
   'c:\\program files\\python\\lib\\site-packages\\win32\\pywintypes38.dll',
   'BINARY'),
  ('pythoncom38.dll',
   'c:\\program '
   'files\\python\\Lib\\site-packages\\pywin32_system32\\pythoncom38.dll',
   'BINARY'),
  ('libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('win32trace',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32ui',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('unicodedata',
   'c:\\program files\\python\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_hashlib', 'c:\\program files\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma', 'c:\\program files\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2', 'c:\\program files\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select', 'c:\\program files\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_overlapped',
   'c:\\program files\\python\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl', 'c:\\program files\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_asyncio', 'c:\\program files\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_ctypes', 'c:\\program files\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing',
   'c:\\program files\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_queue', 'c:\\program files\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('pyexpat', 'c:\\program files\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal', 'c:\\program files\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_testcapi', 'c:\\program files\\python\\DLLs\\_testcapi.pyd', 'EXTENSION'),
  ('_tkinter', 'c:\\program files\\python\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('_elementtree',
   'c:\\program files\\python\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32evtlog',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('_win32sysloader',
   'c:\\program files\\python\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32api',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com.shell.shell',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32wnet',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('_cffi_backend',
   'c:\\program '
   'files\\python\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography.hazmat.bindings._openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt._bcrypt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\bcrypt\\_bcrypt.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_mysql_connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\_mysql_connector.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.period',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reshape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.groupby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.aggregations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.np_datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timestamps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.conversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.ccalendar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.algos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timezones',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.join',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\join.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.vectorized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.parsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.tzconversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\json.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.missing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.timedeltas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.indexing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.ops_dispatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.internals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.interval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.index',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\index.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.sparse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.strptime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.nattype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.writers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.reduction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslibs.offsets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.window.indexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe._speedups',
   'c:\\program '
   'files\\python\\lib\\site-packages\\markupsafe\\_speedups.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cprocessors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cprocessors.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cresultproxy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cresultproxy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy.cutils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cutils.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3', 'c:\\program files\\python\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('MySQLdb._mysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\_mysql.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml.etree',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\etree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml._elementpath',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\_elementpath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('psutil._psutil_windows',
   'c:\\program '
   'files\\python\\lib\\site-packages\\psutil\\_psutil_windows.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32pdh',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas.io.sas._sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas.io.sas._byteswap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.tslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas._libs.hashtable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_socket', 'c:\\program files\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('mfc140u.dll',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\program files\\python\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\program files\\python\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll', 'c:\\program files\\python\\DLLs\\libffi-7.dll', 'BINARY'),
  ('tcl86t.dll', 'c:\\program files\\python\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'c:\\program files\\python\\DLLs\\tk86t.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('sqlite3.dll', 'c:\\program files\\python\\DLLs\\sqlite3.dll', 'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\TortoiseGit\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'D:\\develop\\python\\test\\build\\scan\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tk\\megawidget.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\safe.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\entry.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tk\\tearoff.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tk\\text.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\parray.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tk\\images\\README',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tk\\optMenu.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tk\\tclIndex', 'c:\\program files\\python\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\history.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('Include\\pyconfig.h',
   'c:\\program files\\python\\Include\\pyconfig.h',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\init.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\METADATA',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\METADATA',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\INSTALLER',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\INSTALLER',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\tclIndex', 'c:\\program files\\python\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\clock.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\AUTHORS.rst',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\AUTHORS.rst',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tk\\clrpick.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\posixrules',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\posixrules',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tk\\obsolete.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tk\\console.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('tcl\\tm.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tk\\iconlist.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\top_level.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tk\\images\\logo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\word.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('lib2to3\\tests\\data\\README',
   'c:\\program files\\python\\lib\\lib2to3\\tests\\data\\README',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.PSF',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.PSF',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tk\\palette.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tk\\icons.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tk\\license.terms',
   'c:\\program files\\python\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tk\\scale.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tk\\comdlg.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tcl\\auto.tcl', 'c:\\program files\\python\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.APACHE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tcl\\package.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tk\\choosedir.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tk\\bgerror.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\LICENSE.BSD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\LICENSE.BSD',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\dialog.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tk\\focus.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('lib2to3\\Grammar.txt',
   'c:\\program files\\python\\lib\\lib2to3\\Grammar.txt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tk\\listbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tk\\tk.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\RECORD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\RECORD',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('lib2to3\\PatternGrammar.txt',
   'c:\\program files\\python\\lib\\lib2to3\\PatternGrammar.txt',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tk\\unsupported.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tk\\menu.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('cryptography-3.0-py3.8.egg-info\\WHEEL',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-3.0.dist-info\\WHEEL',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tk\\safetk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('scan.exe.manifest',
   'D:\\develop\\python\\test\\build\\scan\\scan.exe.manifest',
   'BINARY'),
  ('pyi-windows-manifest-filename scan.exe.manifest', '', 'OPTION')],
 False,
 False,
 False,
 [])
