import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# 设置SMTP服务器和端口
smtp_server = 'mail.hnair.com'
port = 587  # 对于TLS 587

# 设置发件人和收件人的邮箱地址
sender_email = "<EMAIL>"
receiver_email = "<EMAIL>"

# 创建邮件对象
message = MIMEMultipart("alternative")
message["Subject"] = "测试邮件"
message["From"] = sender_email
message["To"] = receiver_email

# 添加邮件正文
text = """\
你好，
这是一封测试邮件。
"""
part = MIMEText(text, "plain")
message.attach(part)

try:
    # 连接到SMTP服务器
    server = smtplib.SMTP(smtp_server, port)
    server.starttls()  # 启动安全传输模式

    # 登录SMTP服务器
    server.login(sender_email, 'Mc.zhang_1')

    # 发送邮件
    server.sendmail(sender_email, receiver_email, message.as_string())
    print("邮件发送成功")
except Exception as e:
    print(f"发生错误: {e}")
finally:
    # 关闭连接
    if 'server' in locals():
        server.quit()
