from flask import Flask, request, jsonify
import mysql.connector
from mysql.connector import Error
from decimal import Decimal
import logging
import os

app = Flask(__name__)
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'run.log')),  # 日志保存到 run.log
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
# 数据库连接配置
DB_CONFIG = {
    'host': '************',  # 数据库主机地址
    'user': 'llm_readonly',  # 数据库用户名
    'port': 3306,
    'password': 'LLM%2025Rdv1ew',  # 数据库密码
    'database': 'bi_hna_cost'  # 数据库名称
}


# 连接到MySQL数据库
def get_db_connection():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Error as e:
        logging.error(f"Error connecting to database: {e}")
        return None


# 将结果中的 Decimal 类型转换为 float
def convert_decimal(data):
    if isinstance(data, list):
        return [convert_decimal(item) for item in data]
    elif isinstance(data, dict):
        return {key: convert_decimal(value) for key, value in data.items()}
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data


# 检查SQL语句是否有效
def validate_sql(sql_query):
    sql_query = sql_query.strip().upper()
    if not sql_query.startswith(('SELECT', 'INSERT', 'UPDATE', 'DELETE')):
        raise ValueError("Invalid SQL statement. Only SELECT, INSERT, UPDATE, DELETE are allowed.")
    return sql_query


@app.route('/execute', methods=['POST'])
def execute_sql():
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    if data is None or 'sql' not in data:
        logging.warning("Missing 'sql' parameter")
        return jsonify({"error": "Missing 'sql' parameter"}), 400
    sql_query = data['sql']
    print(sql_query)
    is_echart = data.get('isEchart', True)
    try:
        sql_query = validate_sql(sql_query)
    except Exception as e:
        logging.error(f"Invalid SQL statement: {e}")
        return jsonify({"error": str(e)}), 400
    conn = get_db_connection()
    if conn is None:
        logging.error("Unable to connect to the database")
        return jsonify({"error": "Unable to connect to the database"}), 500
    try:
        cursor = conn.cursor(dictionary=True)
        cursor.execute(sql_query)
        if sql_query.startswith('SELECT'):
            rows = cursor.fetchall()
            converted_rows = convert_decimal(rows)
            if is_echart:
                column_names = list(converted_rows[0].keys()) if converted_rows else []
                if len(column_names) < 2:
                    logging.error("Query result must have at least two columns")
                    return jsonify({"error": "Query result must have at least two columns"}), 400
                x_axis_name = column_names[0]
                y_axis_name = column_names[1]
                xAxis = ";".join([str(row[x_axis_name]) for row in converted_rows])
                yAxis = ";".join([str(row[y_axis_name]) for row in converted_rows])
                return jsonify({"xAxis": xAxis, "yAxis": yAxis})
            else:
                return jsonify(converted_rows)
        else:
            conn.commit()
            logging.info(f"Query executed successfully: {sql_query}")
            return jsonify({"message": "Query executed successfully", "changes": cursor.rowcount})
    except Error as e:
        logging.error(f"Database error: {e}")
        return jsonify({"error": str(e)}), 400
    except KeyError as e:
        logging.error(f"Invalid column name: {e}")
        return jsonify({"error": f"Invalid column name: {str(e)}"}), 400
    finally:
        cursor.close()
        conn.close()


@app.route('/authentication', methods=['POST'])
def authentication():
    if not request.is_json:
        logging.warning("Request is not JSON")
        return jsonify({"error": "Request must be JSON"}), 400
    data = request.get_json()
    logging.info(f"Received authentication request: {data}")
    return jsonify(data)


if __name__ == '__main__':
    logging.info("Starting Flask application")
    app.run(host='0.0.0.0', port=5000, debug=True)
