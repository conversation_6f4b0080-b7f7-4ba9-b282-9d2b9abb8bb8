import scapy.all as scapy
import socket
import concurrent.futures
from datetime import datetime
from sqlalchemy import create_engine, Column, String, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 使用 SQLAlchemy 的基础类
Base = declarative_base()

# 定义设备信息的数据库模型
class Device(Base):
    __tablename__ = 'devices'

    id = Column(Integer, primary_key=True, autoincrement=True)
    ip = Column(String(15), nullable=False)
    mac = Column(String(17), nullable=False)
    hostname = Column(String(255))

# 获取主机名的辅助函数
def get_hostname(ip):
    try:
        return socket.gethostbyaddr(ip)[0]
    except (socket.herror, socket.gaierror):
        return '未知'

# 扫描局域网内在线的设备并尝试获取主机名
def scan(ip_range):
    online_devices = []
    for ip in ip_range:
        arp_request = scapy.ARP(pdst=ip)
        broadcast = scapy.Ether(dst="ff:ff:ff:ff:ff:ff")
        arp_request_broadcast = broadcast / arp_request
        answered_list = scapy.srp(arp_request_broadcast, timeout=1, verbose=False)[0]

        with concurrent.futures.ThreadPoolExecutor() as executor:
            ip_to_hostname = {element[1].psrc: executor.submit(get_hostname, element[1].psrc) for element in answered_list}

            for element in answered_list:
                ip = element[1].psrc
                mac = element[1].hwsrc
                hostname = ip_to_hostname[ip].result()

                online_devices.append({
                    "ip": ip,
                    "mac": mac,
                    "hostname": hostname
                })

    return online_devices

# 将设备信息存储到数据库
def store_to_db(devices, engine):
    Session = sessionmaker(bind=engine)
    session = Session()
    try:
        # 将设备信息插入到数据库
        for device in devices:
            device_record = Device(ip=device['ip'], mac=device['mac'], hostname=device['hostname'])
            session.add(device_record)

        # 提交事务
        session.commit()
        print("设备信息已成功存储到数据库！")
    except Exception as e:
        session.rollback()
        print(f"数据库操作错误: {e}")
    finally:
        session.close()

# IP地址排序的辅助函数
def ip_sort_key(ip):
    return tuple(int(part) for part in ip.split('.'))

# 输出在线设备的信息
def print_result(devices):
    print("在线设备列表：")
    print("IP 地址\t\t\tMAC 地址\t\t\t计算机名")
    print("---------------------------------------------")
    for device in devices:
        print(f"{device['ip']}\t\t{device['mac']}\t\t{device['hostname']}")

# 设置局域网的 IP 范围（根据你的网络配置修改）
ip_range = [f"172.16.113.{i}" for i in range(10, 160)]

# 创建数据库连接（使用 SQLAlchemy 的通用引擎）
DATABASE_URL = "mysql+pymysql://fms_app:B$u#kv$$zx13xz!J@**************:3307/devices?charset=utf8mb4"
engine = create_engine(DATABASE_URL)

# 如果数据库中没有表，则创建表
Base.metadata.create_all(engine)

# 执行扫描并打印结果
devices = scan(ip_range)

if devices:
    # 按 IP 地址从小到大排序
    devices_sorted = sorted(devices, key=lambda device: ip_sort_key(device['ip']))

    # 打印排序后的结果
    print_result(devices_sorted)

    # 将设备信息存储到数据库
    store_to_db(devices_sorted, engine)
