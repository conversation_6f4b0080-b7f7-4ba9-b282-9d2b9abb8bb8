2025-03-06 14:58:48,250 - root - INFO - Starting Flask application
2025-03-06 14:58:48,271 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 14:58:49,271 - root - INFO - Starting Flask application
2025-03-06 14:58:49,277 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 14:58:49,285 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 14:58:50,558 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 14:59:41,786 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 14:59:42,534 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 14:59:42,534 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 14:59:42,537 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 14:59:43,388 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 14:59:43] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 14:59:45,544 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-06 14:59:48,531 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 14:59:48] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:03:36,731 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:03:37,627 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:03:37] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:03:41,574 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-06 15:03:43,564 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:03:43] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:05:48,655 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:05:48,655 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:05:48,655 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:05:49,692 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 15:05:50,747 - root - INFO - Starting Flask application
2025-03-06 15:05:50,754 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 15:05:50,762 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 15:05:54,653 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 15:05:57,314 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:05:57,850 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 15:05:57,851 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 15:05:57,852 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 15:05:59,148 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 15:05:59] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:05:59,456 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:06:00,433 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:06:00] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:06:05,745 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-06 15:06:07,428 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:06:07] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:08:33,537 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:08:34,364 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:08:34] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:08:36,953 - root - INFO - Received request data: [{"indexName":"平均票价"}]
2025-03-06 15:08:38,643 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:08:38] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:10:55,869 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:10:57,224 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:10:57] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:10:59,268 - root - INFO - Received request data: [{"indexName":"平均票价"}]
2025-03-06 15:11:01,994 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:11:01] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:15:07,395 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:15:10,858 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:15:10] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:17:29,264 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:17:31,695 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:17:31] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:17:44,142 - root - INFO - Received request data: <think>
嗯，我现在需要根据用户的问题来选择合适的指标。用户的问题是关于2025年1月份的平均票价趋势，所以我要从给定的指标列表中找出最符合这个问题的那个。

首先，看一下每个指标对应的ask_prompt是什么意思。第一个指标是旅客量，对应提问旅客量时使用，这可能和人数有关，但用户的问题是关于票价，所以这个可能不太相关。第二个是航班量或航段班次，同样，和航班数量相关，虽然票价也会影响航班的价格，但这里主要关注的是平均票价本身。

第三个指标是保变，这通常用于计算机领域，比如保留变量或者状态，这里可能不太适用。第四个是小时油耗，涉及到能源消耗和运营成本，虽然与航空行业有关，但用户的问题不是关于燃油使用效率，而是票价趋势。

第五个指标是平均票价，当提问这个时就应该使用它，所以这可能就是正确的选择。最后一个是客座率，涉及到座位利用率，这和票价关系不大，因为用户关心的是票价的变化，而不是座位的情况。

所以综合来看，平均票价指标最适合回答用户的问题。
</think>

```json
[{"indexName":"平均票价"}]
```
2025-03-06 15:17:44,142 - root - WARNING - Input is not valid JSON
2025-03-06 15:17:44,142 - root - WARNING - Invalid input data
2025-03-06 15:17:44,142 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:17:44] "[31m[1mPOST /getPromptByName HTTP/1.1[0m" 400 -
2025-03-06 15:17:44,595 - root - INFO - Received request data: <think>
嗯，我现在需要根据用户的问题来选择合适的指标。用户的问题是关于2025年1月份的平均票价趋势，所以我要从给定的指标列表中找出最符合这个问题的那个。

首先，看一下每个指标对应的ask_prompt是什么意思。第一个指标是旅客量，对应提问旅客量时使用，这可能和人数有关，但用户的问题是关于票价，所以这个可能不太相关。第二个是航班量或航段班次，同样，和航班数量相关，虽然票价也会影响航班的价格，但这里主要关注的是平均票价本身。

第三个指标是保变，这通常用于计算机领域，比如保留变量或者状态，这里可能不太适用。第四个是小时油耗，涉及到能源消耗和运营成本，虽然与航空行业有关，但用户的问题不是关于燃油使用效率，而是票价趋势。

第五个指标是平均票价，当提问这个时就应该使用它，所以这可能就是正确的选择。最后一个是客座率，涉及到座位利用率，这和票价关系不大，因为用户关心的是票价的变化，而不是座位的情况。

所以综合来看，平均票价指标最适合回答用户的问题。
</think>

```json
[{"indexName":"平均票价"}]
```
2025-03-06 15:17:44,595 - root - WARNING - Input is not valid JSON
2025-03-06 15:17:44,595 - root - WARNING - Invalid input data
2025-03-06 15:17:44,595 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:17:44] "[31m[1mPOST /getPromptByName HTTP/1.1[0m" 400 -
2025-03-06 15:17:45,086 - root - INFO - Received request data: <think>
嗯，我现在需要根据用户的问题来选择合适的指标。用户的问题是关于2025年1月份的平均票价趋势，所以我要从给定的指标列表中找出最符合这个问题的那个。

首先，看一下每个指标对应的ask_prompt是什么意思。第一个指标是旅客量，对应提问旅客量时使用，这可能和人数有关，但用户的问题是关于票价，所以这个可能不太相关。第二个是航班量或航段班次，同样，和航班数量相关，虽然票价也会影响航班的价格，但这里主要关注的是平均票价本身。

第三个指标是保变，这通常用于计算机领域，比如保留变量或者状态，这里可能不太适用。第四个是小时油耗，涉及到能源消耗和运营成本，虽然与航空行业有关，但用户的问题不是关于燃油使用效率，而是票价趋势。

第五个指标是平均票价，当提问这个时就应该使用它，所以这可能就是正确的选择。最后一个是客座率，涉及到座位利用率，这和票价关系不大，因为用户关心的是票价的变化，而不是座位的情况。

所以综合来看，平均票价指标最适合回答用户的问题。
</think>

```json
[{"indexName":"平均票价"}]
```
2025-03-06 15:17:45,086 - root - WARNING - Input is not valid JSON
2025-03-06 15:17:45,086 - root - WARNING - Invalid input data
2025-03-06 15:17:45,086 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:17:45] "[31m[1mPOST /getPromptByName HTTP/1.1[0m" 400 -
2025-03-06 15:17:45,537 - root - INFO - Received request data: <think>
嗯，我现在需要根据用户的问题来选择合适的指标。用户的问题是关于2025年1月份的平均票价趋势，所以我要从给定的指标列表中找出最符合这个问题的那个。

首先，看一下每个指标对应的ask_prompt是什么意思。第一个指标是旅客量，对应提问旅客量时使用，这可能和人数有关，但用户的问题是关于票价，所以这个可能不太相关。第二个是航班量或航段班次，同样，和航班数量相关，虽然票价也会影响航班的价格，但这里主要关注的是平均票价本身。

第三个指标是保变，这通常用于计算机领域，比如保留变量或者状态，这里可能不太适用。第四个是小时油耗，涉及到能源消耗和运营成本，虽然与航空行业有关，但用户的问题不是关于燃油使用效率，而是票价趋势。

第五个指标是平均票价，当提问这个时就应该使用它，所以这可能就是正确的选择。最后一个是客座率，涉及到座位利用率，这和票价关系不大，因为用户关心的是票价的变化，而不是座位的情况。

所以综合来看，平均票价指标最适合回答用户的问题。
</think>

```json
[{"indexName":"平均票价"}]
```
2025-03-06 15:17:45,538 - root - WARNING - Input is not valid JSON
2025-03-06 15:17:45,538 - root - WARNING - Invalid input data
2025-03-06 15:17:45,538 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:17:45] "[31m[1mPOST /getPromptByName HTTP/1.1[0m" 400 -
2025-03-06 15:21:32,549 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-06 15:21:32,550 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\codecs.cpython-38.pyc', reloading
2025-03-06 15:21:32,552 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\aliases.cpython-38.pyc', reloading
2025-03-06 15:21:32,553 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\utf_8.cpython-38.pyc', reloading
2025-03-06 15:21:32,554 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\gbk.cpython-38.pyc', reloading
2025-03-06 15:21:32,554 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\latin_1.cpython-38.pyc', reloading
2025-03-06 15:21:32,555 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\io.cpython-38.pyc', reloading
2025-03-06 15:21:32,556 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-06 15:21:32,556 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\site.cpython-38.pyc', reloading
2025-03-06 15:21:32,557 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\os.cpython-38.pyc', reloading
2025-03-06 15:21:32,558 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\stat.cpython-38.pyc', reloading
2025-03-06 15:21:32,558 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_collections_abc.cpython-38.pyc', reloading
2025-03-06 15:21:32,559 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\ntpath.cpython-38.pyc', reloading
2025-03-06 15:21:32,560 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\genericpath.cpython-38.pyc', reloading
2025-03-06 15:21:32,561 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_sitebuiltins.cpython-38.pyc', reloading
2025-03-06 15:21:32,563 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_bootlocale.cpython-38.pyc', reloading
2025-03-06 15:21:32,564 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\types.cpython-38.pyc', reloading
2025-03-06 15:21:32,565 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-06 15:21:32,566 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\warnings.cpython-38.pyc', reloading
2025-03-06 15:21:32,567 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\util.cpython-38.pyc', reloading
2025-03-06 15:21:32,568 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-06 15:21:32,568 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\machinery.cpython-38.pyc', reloading
2025-03-06 15:21:32,571 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\contextlib.cpython-38.pyc', reloading
2025-03-06 15:21:32,572 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-06 15:21:32,574 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\operator.cpython-38.pyc', reloading
2025-03-06 15:21:32,575 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\keyword.cpython-38.pyc', reloading
2025-03-06 15:21:32,577 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\heapq.cpython-38.pyc', reloading
2025-03-06 15:21:32,578 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\reprlib.cpython-38.pyc', reloading
2025-03-06 15:21:32,581 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\functools.cpython-38.pyc', reloading
2025-03-06 15:21:32,601 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\site-packages\\win32\\lib\\__pycache__\\pywin32_bootstrap.cpython-38.pyc', reloading
2025-03-06 15:21:32,622 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\__future__.cpython-38.pyc', reloading
2025-03-06 15:21:32,623 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\inspect.cpython-38.pyc', reloading
2025-03-06 15:21:32,623 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\dis.cpython-38.pyc', reloading
2025-03-06 15:21:32,624 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\opcode.cpython-38.pyc', reloading
2025-03-06 15:21:32,626 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-06 15:21:32,626 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\enum.cpython-38.pyc', reloading
2025-03-06 15:21:32,627 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\linecache.cpython-38.pyc', reloading
2025-03-06 15:21:32,628 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\tokenize.cpython-38.pyc', reloading
2025-03-06 15:21:32,628 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\re.cpython-38.pyc', reloading
2025-03-06 15:21:32,629 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_compile.cpython-38.pyc', reloading
2025-03-06 15:21:32,630 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_parse.cpython-38.pyc', reloading
2025-03-06 15:21:32,631 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_constants.cpython-38.pyc', reloading
2025-03-06 15:21:32,631 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\copyreg.cpython-38.pyc', reloading
2025-03-06 15:21:32,632 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\token.cpython-38.pyc', reloading
2025-03-06 15:21:32,634 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\bisect.cpython-38.pyc', reloading
2025-03-06 15:21:32,635 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\fnmatch.cpython-38.pyc', reloading
2025-03-06 15:21:32,635 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\posixpath.cpython-38.pyc', reloading
2025-03-06 15:21:32,636 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\optparse.cpython-38.pyc', reloading
2025-03-06 15:21:32,637 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\textwrap.cpython-38.pyc', reloading
2025-03-06 15:21:32,638 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\gettext.cpython-38.pyc', reloading
2025-03-06 15:21:32,639 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\locale.cpython-38.pyc', reloading
2025-03-06 15:21:32,641 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\configparser.cpython-38.pyc', reloading
2025-03-06 15:21:32,645 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\signal.cpython-38.pyc', reloading
2025-03-06 15:21:34,238 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 15:21:35,243 - root - INFO - Starting Flask application
2025-03-06 15:21:35,249 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 15:21:35,257 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 15:21:36,630 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 15:22:34,110 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:22:34,112 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:22:35,139 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 15:25:50,292 - root - INFO - Starting Flask application
2025-03-06 15:25:50,314 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 15:25:51,348 - root - INFO - Starting Flask application
2025-03-06 15:25:51,354 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 15:25:51,362 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 15:25:55,776 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 15:25:57,281 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:25:58,130 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 15:25:58,130 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 15:25:58,131 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 15:25:58,857 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:25:58] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:26:02,158 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:26:02,359 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:26:05,286 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:26:05] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:26:05,522 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 15:26:05] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:31:27,639 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:31:27,640 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:31:27,640 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 15:31:28,673 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 15:48:03,513 - root - INFO - Starting Flask application
2025-03-06 15:48:03,540 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 15:48:04,532 - root - INFO - Starting Flask application
2025-03-06 15:48:04,539 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 15:48:04,546 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 15:48:05,853 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 15:48:07,705 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:48:08,541 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 15:48:08,541 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 15:48:08,542 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\__pycache__\\mysql_native_password.cpython-38.pyc', reloading
2025-03-06 15:48:08,542 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 15:48:09,578 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 15:48:10,547 - root - INFO - Starting Flask application
2025-03-06 15:48:10,554 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 15:48:10,561 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 15:48:10,680 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 15:48:20,381 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:48:20,855 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 15:48:20,856 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 15:48:20,857 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 15:48:23,221 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 15:48:23] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:48:31,100 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:48:31,981 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:48:31] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:48:34,783 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:48:36,636 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:48:36] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:49:49,319 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:49:50,435 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:49:50] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:49:53,050 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:49:55,135 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:49:55] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:50:12,276 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:50:13,534 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:50:13] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:50:15,997 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:50:17,740 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:50:17] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:50:44,023 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:50:44,923 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:50:44] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:50:47,599 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:50:49,592 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:50:49] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:51:38,273 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:51:39,183 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:51:39] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:51:41,491 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:51:43,353 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:51:43] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:51:58,596 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:51:59,476 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:51:59] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:52:02,210 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:52:04,192 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:52:04] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:52:27,350 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:52:28,291 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:52:28] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:52:31,025 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:52:32,864 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:52:32] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 15:52:50,347 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 15:52:51,042 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:52:51] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 15:52:53,274 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 15:52:55,057 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 15:52:55] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:03:20,848 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\appv1.5.py', reloading
2025-03-06 16:03:20,848 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\appv1.5.py', reloading
2025-03-06 16:03:20,849 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\appv1.5.py', reloading
2025-03-06 16:03:21,927 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 16:03:23,297 - root - INFO - Starting Flask application
2025-03-06 16:03:23,309 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 16:03:23,326 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 16:03:24,914 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 16:03:30,757 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:03:30,757 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:03:30,758 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:03:32,202 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 16:03:33,341 - root - INFO - Starting Flask application
2025-03-06 16:03:33,349 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 16:03:33,357 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 16:03:34,679 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 16:05:19,437 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:05:19,437 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:05:20,476 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 16:05:21,560 - root - INFO - Starting Flask application
2025-03-06 16:05:21,567 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 16:05:21,576 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 16:05:24,066 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 16:06:14,235 - root - INFO - Starting Flask application
2025-03-06 16:06:14,259 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 16:06:15,363 - root - INFO - Starting Flask application
2025-03-06 16:06:15,370 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 16:06:15,379 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 16:06:16,736 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 16:07:27,237 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 16:07:43,364 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 16:07:43,365 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 16:07:43,366 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 16:09:30,153 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 16:09:30] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:09:41,636 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 16:09:48,776 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 16:09:48] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:12:03,338 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:12:04,850 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:12:04] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:12:27,268 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 16:12:32,900 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:12:32] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:14:50,833 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 16:14:52,391 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 16:14:52] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:17:13,824 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:17:13,824 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:17:13,824 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:17:15,378 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 16:17:16,368 - root - INFO - Starting Flask application
2025-03-06 16:17:16,374 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 16:17:16,381 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 16:17:16,619 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 16:17:52,087 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:17:52,853 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 16:17:52,853 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 16:17:52,854 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 16:17:53,956 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:17:53] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:17:56,093 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 16:17:57,653 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:17:57] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:18:06,943 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(leg_num) AS flight_count FROM vw_flight_revenue_cost WHERE airline_code_stats = '8L' AND flight_date LIKE '%-01-%' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-06 16:18:09,369 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:18:09] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 16:21:42,040 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:21:43,442 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:21:43] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:21:46,707 - root - INFO - Received request data: [{"indexName":"航班量"}]
2025-03-06 16:21:47,975 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:21:47] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:22:17,781 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(leg_num) AS flight_volume FROM vw_flight_revenue_cost WHERE airline_code_stats = '8L' AND flight_date LIKE '%-01-%' GROUP BY flight_date;"}
2025-03-06 16:22:19,765 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:22:19] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 16:27:00,852 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:27:02,375 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:27:02] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:27:04,820 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:27:06,142 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:27:06] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:27:44,554 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(fuel_consumption) / SUM(flight_hours) / 1000 AS hourly_fuel_consumption FROM flight_data WHERE airline_code_stats = '8L' AND flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-06 16:27:45,720 - root - ERROR - Database error on db_bi: 1142 (42000): SELECT command denied to user 'llm_readonly'@'10.63.25.254' for table 'flight_data'
2025-03-06 16:27:45,721 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:27:45] "[31m[1mPOST /execute HTTP/1.1[0m" 400 -
2025-03-06 16:33:20,902 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:33:24,768 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:33:24] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:33:28,065 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:33:30,804 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:33:30] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:33:38,605 - root - INFO - Received request data: {'sql': "SELECT flight_date, (SUM(fuel_consumption) / 1000) / SUM(flight_hours) AS hourly_fuel_consumption FROM flight_data WHERE airline_code_stats = '8L' AND flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-06 16:33:41,150 - root - ERROR - Database error on db_bi: 1142 (42000): SELECT command denied to user 'llm_readonly'@'10.63.25.254' for table 'flight_data'
2025-03-06 16:33:41,151 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:33:41] "[31m[1mPOST /execute HTTP/1.1[0m" 400 -
2025-03-06 16:35:23,822 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:35:25,048 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 16:35:25] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:36:48,910 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-06 16:36:48,911 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\codecs.cpython-38.pyc', reloading
2025-03-06 16:36:48,912 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\aliases.cpython-38.pyc', reloading
2025-03-06 16:36:48,912 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\utf_8.cpython-38.pyc', reloading
2025-03-06 16:36:48,913 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\gbk.cpython-38.pyc', reloading
2025-03-06 16:36:48,913 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\latin_1.cpython-38.pyc', reloading
2025-03-06 16:36:48,914 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\io.cpython-38.pyc', reloading
2025-03-06 16:36:48,915 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-06 16:36:48,916 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\site.cpython-38.pyc', reloading
2025-03-06 16:36:48,916 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\os.cpython-38.pyc', reloading
2025-03-06 16:36:48,917 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\stat.cpython-38.pyc', reloading
2025-03-06 16:36:48,917 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_collections_abc.cpython-38.pyc', reloading
2025-03-06 16:36:48,919 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\ntpath.cpython-38.pyc', reloading
2025-03-06 16:36:48,919 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\genericpath.cpython-38.pyc', reloading
2025-03-06 16:36:48,921 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_sitebuiltins.cpython-38.pyc', reloading
2025-03-06 16:36:48,922 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_bootlocale.cpython-38.pyc', reloading
2025-03-06 16:36:48,924 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\types.cpython-38.pyc', reloading
2025-03-06 16:36:48,926 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-06 16:36:48,927 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\warnings.cpython-38.pyc', reloading
2025-03-06 16:36:48,928 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\util.cpython-38.pyc', reloading
2025-03-06 16:36:48,929 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-06 16:36:48,930 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\machinery.cpython-38.pyc', reloading
2025-03-06 16:36:48,931 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\contextlib.cpython-38.pyc', reloading
2025-03-06 16:36:48,933 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-06 16:36:48,934 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\operator.cpython-38.pyc', reloading
2025-03-06 16:36:48,935 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\keyword.cpython-38.pyc', reloading
2025-03-06 16:36:48,936 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\heapq.cpython-38.pyc', reloading
2025-03-06 16:36:48,936 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\reprlib.cpython-38.pyc', reloading
2025-03-06 16:36:48,938 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\functools.cpython-38.pyc', reloading
2025-03-06 16:36:48,947 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\site-packages\\win32\\lib\\__pycache__\\pywin32_bootstrap.cpython-38.pyc', reloading
2025-03-06 16:36:48,967 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\__future__.cpython-38.pyc', reloading
2025-03-06 16:36:48,967 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\inspect.cpython-38.pyc', reloading
2025-03-06 16:36:48,968 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\dis.cpython-38.pyc', reloading
2025-03-06 16:36:48,968 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\opcode.cpython-38.pyc', reloading
2025-03-06 16:36:48,971 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-06 16:36:48,971 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\enum.cpython-38.pyc', reloading
2025-03-06 16:36:48,972 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\linecache.cpython-38.pyc', reloading
2025-03-06 16:36:48,972 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\tokenize.cpython-38.pyc', reloading
2025-03-06 16:36:48,973 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\re.cpython-38.pyc', reloading
2025-03-06 16:36:48,974 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_compile.cpython-38.pyc', reloading
2025-03-06 16:36:48,974 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_parse.cpython-38.pyc', reloading
2025-03-06 16:36:48,975 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_constants.cpython-38.pyc', reloading
2025-03-06 16:36:48,976 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\copyreg.cpython-38.pyc', reloading
2025-03-06 16:36:48,977 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\token.cpython-38.pyc', reloading
2025-03-06 16:36:48,978 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\bisect.cpython-38.pyc', reloading
2025-03-06 16:36:48,979 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\fnmatch.cpython-38.pyc', reloading
2025-03-06 16:36:48,979 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\posixpath.cpython-38.pyc', reloading
2025-03-06 16:36:48,980 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\optparse.cpython-38.pyc', reloading
2025-03-06 16:36:48,981 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\textwrap.cpython-38.pyc', reloading
2025-03-06 16:36:48,982 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\gettext.cpython-38.pyc', reloading
2025-03-06 16:36:48,983 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\locale.cpython-38.pyc', reloading
2025-03-06 16:36:48,985 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\configparser.cpython-38.pyc', reloading
2025-03-06 16:36:48,990 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\signal.cpython-38.pyc', reloading
2025-03-06 16:36:50,045 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 16:36:51,084 - root - INFO - Starting Flask application
2025-03-06 16:36:51,092 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 16:36:51,102 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 16:36:52,554 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 16:37:22,714 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:37:22,714 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:37:22,715 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-06 16:37:23,753 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-06 16:37:24,760 - root - INFO - Starting Flask application
2025-03-06 16:37:24,766 - werkzeug - WARNING -  * Debugger is active!
2025-03-06 16:37:24,774 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-06 16:37:26,054 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-06 16:37:33,618 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:37:33,619 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 16:37:34,547 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-06 16:37:34,547 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-06 16:37:34,547 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-06 16:37:35,421 - werkzeug - INFO - 172.16.113.16 - - [06/Mar/2025 16:37:35] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:38:59,461 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:39:00,945 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:39:00] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:39:03,866 - root - INFO - Received request data: [{"indexName":"旅客量"}]
2025-03-06 16:39:03,866 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '旅客量' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 16:39:05,504 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:39:05] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:39:17,252 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(pax_num) AS passenger_volume FROM vw_flight_revenue_cost WHERE airline_code_stats = '8L' AND flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-06 16:39:18,683 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:39:18] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 16:42:22,880 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:42:24,362 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:42:24] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:42:28,533 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:42:28,533 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 16:42:31,083 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:42:31] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:42:38,936 - root - INFO - Received request data: {'sql': "SELECT aircraft_type, SUM(fuel_consumption) / SUM(flight_hours) / 1000 AS hourly_fuel_consumption FROM flight_data WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY aircraft_type;"}
2025-03-06 16:42:40,471 - root - ERROR - Database error on db_bi: 1142 (42000): SELECT command denied to user 'llm_readonly'@'10.63.25.254' for table 'flight_data'
2025-03-06 16:42:40,472 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:42:40] "[31m[1mPOST /execute HTTP/1.1[0m" 400 -
2025-03-06 16:44:37,763 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:44:39,345 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:44:39] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:44:41,443 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:44:41,443 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 16:44:43,134 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:44:43] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:44:53,773 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-06 16:44:59,976 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:44:59] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 16:54:01,011 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:54:01,553 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:54:01] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:54:03,724 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:54:03,726 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 16:54:04,410 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:54:04] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 16:54:16,883 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-06 16:54:19,682 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:54:19] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 16:59:54,202 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 16:59:55,047 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:59:55] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 16:59:57,380 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 16:59:57,380 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 16:59:58,209 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 16:59:58] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 17:00:13,715 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-06 17:00:16,013 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 17:00:16] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 17:28:31,641 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 17:28:32,566 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 17:28:32] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 17:28:34,308 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 17:28:34,309 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 17:28:35,229 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 17:28:35] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 17:28:41,345 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / 1000 / SUM(flight_hour) AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date >= '2025-01-01' AND flight_date <= '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-06 17:28:43,826 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 17:28:43] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 17:29:46,525 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 17:29:47,465 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 17:29:47] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 17:29:49,109 - root - INFO - Received request data: [{"indexName":"旅客量"}]
2025-03-06 17:29:49,109 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '旅客量' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 17:29:50,027 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 17:29:50] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 17:29:54,076 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(pax_num) AS passenger_volume FROM vw_flight_revenue_cost WHERE airline_code_stats = '8L' AND flight_date >= '2025-01-01' AND flight_date <= '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-06 17:29:55,097 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 17:29:55] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:12:32,311 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:12:32,793 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:12:32] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:12:34,524 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 19:12:34,524 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:12:35,044 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:12:35] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:12:39,511 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-06 19:12:46,290 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:12:46] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:50:23,668 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:50:24,526 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:50:24] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:50:26,531 - root - INFO - Received request data: [{"indexName":"客座率"}, {"indexName":"平均票价"}]
2025-03-06 19:50:26,531 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '客座率' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:50:27,343 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:50:27] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:50:31,617 - root - INFO - Received request data: {'sql': "SELECT flight_date, (rpk / ask) AS load_factor, (passenger_revenue / pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats IN ('GS', '8L') AND flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY flight_date;"}
2025-03-06 19:50:33,045 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:50:33] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:51:04,212 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:51:05,218 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:51:05] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:51:06,907 - root - INFO - Received request data: [{"indexName":"客座率"},{"indexName":"平均票价"}]
2025-03-06 19:51:06,907 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '客座率' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:51:07,731 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:51:07] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:51:11,942 - root - INFO - Received request data: {'sql': "SELECT flight_date, (rpk / ask) AS load_factor, (passenger_revenue / pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats IN ('GS', '8L') AND flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY flight_date;"}
2025-03-06 19:51:13,280 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:51:13] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:53:38,210 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:53:39,112 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:53:39] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:53:40,790 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 19:53:40,790 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:53:41,718 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:53:41] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:53:46,662 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' AND ac_small_type = '320' GROUP BY airline_code_stats ORDER BY hourly_fuel_consumption ASC;"}
2025-03-06 19:53:48,861 - root - ERROR - Query result must have at least two columns
2025-03-06 19:53:48,861 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:53:48] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:55:42,925 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:55:43,824 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:55:43] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:55:45,867 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 19:55:45,868 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:55:46,710 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:55:46] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:55:51,099 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' AND ac_small_type = '320' AND airline_code_stats IN ('HU','CN') GROUP BY flight_date;"}
2025-03-06 19:55:52,513 - root - ERROR - Query result must have at least two columns
2025-03-06 19:55:52,514 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:55:52] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:56:51,936 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:56:52,836 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:56:52] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:56:54,813 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 19:56:54,813 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:56:55,652 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:56:55] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:57:00,737 - root - INFO - Received request data: {'sql': "SELECT YEARWEEK(flight_date) AS week, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE ac_small_type = '320' AND airline_code_stats = 'HU' AND YEAR(flight_date) = YEAR(CURDATE()) GROUP BY YEARWEEK(flight_date);"}
2025-03-06 19:57:02,118 - root - ERROR - Query result must have at least two columns
2025-03-06 19:57:02,119 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:57:02] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:57:30,274 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:57:31,177 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:57:31] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:57:32,781 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 19:57:32,781 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:57:33,664 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:57:33] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:57:37,595 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' AND ac_small_type = '320' AND airline_code_stats = 'HU' GROUP BY flight_date;"}
2025-03-06 19:57:38,935 - root - ERROR - Query result must have at least two columns
2025-03-06 19:57:38,937 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:57:38] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:58:09,080 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:58:09,956 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:58:09] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:58:11,571 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 19:58:11,571 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:58:12,490 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:58:12] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:58:15,982 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE ac_kind_type = '宽体机' AND YEAR(flight_date) = YEAR(CURDATE()) GROUP BY airline_code_stats;"}
2025-03-06 19:58:18,094 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:58:18] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 19:59:12,574 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 19:59:13,452 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:59:13] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 19:59:15,140 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-06 19:59:15,140 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 19:59:16,071 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:59:16] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 19:59:20,390 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' AND ac_kind_type = '宽体机' GROUP BY airline_code_stats;"}
2025-03-06 19:59:22,584 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 19:59:22] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 20:05:19,546 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 20:05:20,464 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:05:20] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 20:05:22,836 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-06 20:05:22,837 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '保变' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 20:05:24,171 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:05:24] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 20:05:28,401 - root - INFO - Received request data: {'sql': "SELECT COUNT(*) AS non_protected_flight_count FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' AND (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25;"}
2025-03-06 20:05:30,491 - root - ERROR - Query result must have at least two columns
2025-03-06 20:05:30,492 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:05:30] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 20:05:47,538 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 20:05:48,020 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:05:48] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 20:05:55,530 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-06 20:05:55,530 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '保变' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 20:05:56,048 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:05:56] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 20:06:00,524 - root - INFO - Received request data: {'sql': "SELECT flight_date, COUNT(*) AS non_guaranteed_flight_count FROM vw_flight_revenue_cost WHERE (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25 AND airline_code_stats = 'HU' AND flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-06 20:06:01,454 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:06:01] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 20:11:50,088 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 20:11:50,589 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:11:50] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 20:11:52,361 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-06 20:11:52,361 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '保变' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 20:11:52,864 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:11:52] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 20:11:57,467 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, COUNT(*) AS non_profit_segment_count FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' AND (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25 GROUP BY airline_code_stats;"}
2025-03-06 20:11:59,364 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:11:59] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-06 20:14:56,713 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-06 20:14:57,175 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:14:57] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-06 20:14:59,153 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-06 20:14:59,154 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '保变' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-06 20:14:59,695 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:14:59] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-06 20:15:05,927 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(CASE WHEN (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25 THEN leg_num ELSE 0 END) / SUM(leg_num) AS non_guaranteed_ratio FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY airline_code_stats ORDER BY non_guaranteed_ratio DESC;"}
2025-03-06 20:15:07,906 - werkzeug - INFO - 172.16.113.247 - - [06/Mar/2025 20:15:07] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 08:34:07,210 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 08:34:09,731 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:34:09] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 08:34:11,442 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-07 08:34:11,442 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:34:13,091 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:34:13] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:34:16,340 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-07 08:34:16,340 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:34:16,817 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-07 08:34:18,217 - werkzeug - INFO - 172.16.113.16 - - [07/Mar/2025 08:34:18] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:34:27,935 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:34:27] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 08:35:59,660 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 08:36:01,083 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:36:01] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 08:36:03,976 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-07 08:36:03,976 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:36:05,562 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:36:05] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:36:10,691 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-07 08:36:13,452 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:36:13] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 08:39:54,329 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\latin_1.cpython-38.pyc', reloading
2025-03-07 08:39:54,372 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 08:39:54,373 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\codecs.cpython-38.pyc', reloading
2025-03-07 08:39:54,375 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\aliases.cpython-38.pyc', reloading
2025-03-07 08:39:54,375 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\utf_8.cpython-38.pyc', reloading
2025-03-07 08:39:54,376 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\gbk.cpython-38.pyc', reloading
2025-03-07 08:39:54,376 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\latin_1.cpython-38.pyc', reloading
2025-03-07 08:39:54,377 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\io.cpython-38.pyc', reloading
2025-03-07 08:39:54,378 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-07 08:39:54,379 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\site.cpython-38.pyc', reloading
2025-03-07 08:39:54,379 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\os.cpython-38.pyc', reloading
2025-03-07 08:39:54,381 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\stat.cpython-38.pyc', reloading
2025-03-07 08:39:54,382 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_collections_abc.cpython-38.pyc', reloading
2025-03-07 08:39:54,384 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\ntpath.cpython-38.pyc', reloading
2025-03-07 08:39:54,385 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\genericpath.cpython-38.pyc', reloading
2025-03-07 08:39:54,387 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_sitebuiltins.cpython-38.pyc', reloading
2025-03-07 08:39:54,389 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_bootlocale.cpython-38.pyc', reloading
2025-03-07 08:39:54,390 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\types.cpython-38.pyc', reloading
2025-03-07 08:39:54,392 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 08:39:54,392 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\warnings.cpython-38.pyc', reloading
2025-03-07 08:39:54,393 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\util.cpython-38.pyc', reloading
2025-03-07 08:39:54,394 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-07 08:39:54,395 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\machinery.cpython-38.pyc', reloading
2025-03-07 08:39:54,396 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\contextlib.cpython-38.pyc', reloading
2025-03-07 08:39:54,397 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 08:39:54,398 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\operator.cpython-38.pyc', reloading
2025-03-07 08:39:54,399 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\keyword.cpython-38.pyc', reloading
2025-03-07 08:39:54,399 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\heapq.cpython-38.pyc', reloading
2025-03-07 08:39:54,400 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\reprlib.cpython-38.pyc', reloading
2025-03-07 08:39:54,402 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\functools.cpython-38.pyc', reloading
2025-03-07 08:39:54,414 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\site-packages\\win32\\lib\\__pycache__\\pywin32_bootstrap.cpython-38.pyc', reloading
2025-03-07 08:39:54,583 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 08:39:55,827 - root - INFO - Starting Flask application
2025-03-07 08:39:55,834 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 08:39:55,844 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 08:39:57,480 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 08:40:02,878 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 08:40:02,878 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 08:40:02,878 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 08:40:03,911 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 08:40:05,080 - root - INFO - Starting Flask application
2025-03-07 08:40:05,088 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 08:40:05,097 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 08:40:06,655 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 08:41:26,144 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-07 08:41:26,145 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:41:27,162 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 08:41:27,162 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 08:41:27,164 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\__pycache__\\mysql_native_password.cpython-38.pyc', reloading
2025-03-07 08:41:27,164 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 08:41:28,224 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 08:41:29,455 - root - INFO - Starting Flask application
2025-03-07 08:41:29,462 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 08:41:29,471 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 08:41:30,964 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 08:41:35,566 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-07 08:41:35,566 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:41:36,666 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 08:41:36,666 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 08:41:36,667 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 08:41:37,585 - werkzeug - INFO - 172.16.113.16 - - [07/Mar/2025 08:41:37] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:42:04,609 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 08:42:07,652 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:42:07] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 08:42:14,916 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-07 08:42:14,917 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:42:20,998 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:42:20] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:42:28,997 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date >= '2025-01-01' AND flight_date <= '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-07 08:42:33,408 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:42:33] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 08:45:01,837 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 08:45:04,103 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:45:04] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 08:45:06,618 - root - INFO - Received request data: [{"indexName":"小时油耗"}]
2025-03-07 08:45:06,619 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '小时油耗' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:45:09,199 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:45:09] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:45:13,645 - root - INFO - Received request data: {'sql': "SELECT ac_kind_type, SUM(oil_consumption) / SUM(flight_hour) / 1000 AS hourly_fuel_consumption FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY ac_kind_type;"}
2025-03-07 08:45:18,002 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:45:18] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 08:48:43,335 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 08:48:46,214 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:48:46] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 08:48:47,972 - root - INFO - Received request data: [{"indexName":"平均票价"}]
2025-03-07 08:48:47,972 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:48:51,144 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:48:51] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:48:54,883 - root - INFO - Received request data: {'sql': "SELECT SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date >= '2025-01-01' AND flight_date <= '2025-01-31';"}
2025-03-07 08:48:58,871 - root - ERROR - Query result must have at least two columns
2025-03-07 08:48:58,872 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:48:58] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 08:50:47,311 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 08:50:49,483 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:50:49] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 08:50:51,325 - root - INFO - Received request data: [{"indexName":"平均票价"}]
2025-03-07 08:50:51,325 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:50:53,450 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:50:53] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:50:57,122 - root - INFO - Received request data: {'sql': "SELECT SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date >= '2025-01-01' AND flight_date <= '2025-01-31';"}
2025-03-07 08:50:58,818 - root - ERROR - Query result must have at least two columns
2025-03-07 08:50:58,819 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:50:58] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 08:53:19,454 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 08:53:22,351 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:53:22] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 08:53:24,384 - root - INFO - Received request data: [{"indexName":"平均票价"}]
2025-03-07 08:53:24,384 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 08:53:26,545 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:53:26] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 08:53:30,981 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date >= '2025-01-01' AND flight_date <= '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-07 08:53:32,926 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 08:53:32] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 09:00:18,257 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 09:00:19,578 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:00:19] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 09:00:21,603 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 09:00:21,603 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 09:00:22,254 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:00:22] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 09:46:13,497 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 09:46:16,324 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:46:16] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 09:46:18,334 - root - INFO - Received request data: [{"indexName":"保变"}]
2025-03-07 09:46:18,334 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '保变' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 09:46:22,051 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:46:22] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 09:46:27,785 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(CASE WHEN (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25 THEN leg_num ELSE 0 END) / SUM(leg_num) AS non_profit_ratio FROM vw_flight_revenue_cost WHERE flight_date BETWEEN '2025-01-01' AND '2025-01-31' GROUP BY airline_code_stats;"}
2025-03-07 09:46:35,487 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:46:35] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 09:47:21,834 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 09:47:25,273 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:47:25] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 09:47:27,260 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 09:47:27,260 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 09:47:30,058 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:47:30] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 09:47:33,852 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date >= '2025-01-01' AND flight_date <= '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-07 09:47:36,580 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:47:36] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 09:55:26,472 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 09:55:30,021 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:55:30] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 09:55:32,119 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 09:55:32,119 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 09:55:36,904 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:55:36] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 09:55:40,739 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date >= '2025-01-01' AND flight_date <= '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-07 09:55:45,515 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 09:55:45] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 10:01:10,259 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 10:01:15,186 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 10:01:15] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 10:01:17,204 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 10:01:17,204 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 10:01:23,390 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 10:01:23] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 10:01:27,555 - root - INFO - Received request data: {'sql': "SELECT flight_date, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date >= '2025-01-01' AND flight_date <= '2025-01-31' GROUP BY flight_date ORDER BY flight_date;"}
2025-03-07 10:01:32,072 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 10:01:32] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 10:04:17,306 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 10:04:18,355 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 10:04:19,518 - root - INFO - Starting Flask application
2025-03-07 10:04:19,526 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 10:04:19,536 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 10:04:20,167 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 10:15:00,164 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\optparse.cpython-38.pyc', reloading
2025-03-07 10:15:00,166 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\gettext.cpython-38.pyc', reloading
2025-03-07 10:15:01,210 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 10:15:02,355 - root - INFO - Starting Flask application
2025-03-07 10:15:02,363 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 10:15:02,372 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 10:15:07,278 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 10:15:36,418 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 10:15:36,419 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 10:15:36,420 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 10:15:37,462 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 10:15:38,616 - root - INFO - Starting Flask application
2025-03-07 10:15:38,624 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 10:15:38,635 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 10:15:38,849 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 10:15:53,427 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 10:15:54,286 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 10:15:54,286 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 10:15:54,288 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\__pycache__\\mysql_native_password.cpython-38.pyc', reloading
2025-03-07 10:15:54,288 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 10:15:54,974 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 10:15:54] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 10:15:55,321 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 10:15:56,458 - root - INFO - Starting Flask application
2025-03-07 10:15:56,466 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 10:15:56,474 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 10:15:57,833 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 10:15:57,999 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 10:15:57,999 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 10:15:58,553 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 10:15:58,553 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 10:15:58,554 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 10:15:59,130 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 10:15:59] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 10:16:02,949 - root - INFO - Received request data: {'sql': 'SELECT flight_date, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE YEAR(flight_date) = YEAR(CURDATE()) GROUP BY flight_date;'}
2025-03-07 10:16:04,249 - root - ERROR - Query result must have at least two columns
2025-03-07 10:16:04,257 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\site-packages\\flask\\app.py', reloading
2025-03-07 10:16:04,258 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\site-packages\\flask\\_compat.py', reloading
2025-03-07 10:16:04,259 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 10:16:04] "[35m[1mPOST /execute HTTP/1.1[0m" 500 -
2025-03-07 10:16:05,294 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 10:16:06,378 - root - INFO - Starting Flask application
2025-03-07 10:16:06,385 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 10:16:06,393 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 10:16:07,714 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 13:26:23,969 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 13:26:45,009 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\locales\\eng\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 13:26:45,012 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\locales\\eng\\__pycache__\\client_error.cpython-38.pyc', reloading
2025-03-07 13:26:45,014 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:26:45,014 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:26:45,015 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:26:45] "[31m[1mGET /getIndexTree HTTP/1.1[0m" 400 -
2025-03-07 13:26:45,475 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 13:26:46,071 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 13:26:47,464 - root - INFO - Starting Flask application
2025-03-07 13:26:47,471 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 13:26:47,481 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 13:26:47,540 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 13:26:48,648 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 13:26:48,648 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 13:27:09,701 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:27:09,701 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:27:09,701 - root - ERROR - Error executing query for index '平均票价': Unable to connect to the database: db_wiki
2025-03-07 13:27:09,702 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:27:09] "[35m[1mPOST /getPromptByName HTTP/1.1[0m" 500 -
2025-03-07 13:27:10,162 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 13:27:10,162 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 13:27:31,189 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:27:31,190 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:27:31,190 - root - ERROR - Error executing query for index '平均票价': Unable to connect to the database: db_wiki
2025-03-07 13:27:31,190 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:27:31] "[35m[1mPOST /getPromptByName HTTP/1.1[0m" 500 -
2025-03-07 13:27:31,627 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 13:27:31,627 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 13:27:52,678 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:27:52,678 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:27:52,678 - root - ERROR - Error executing query for index '平均票价': Unable to connect to the database: db_wiki
2025-03-07 13:27:52,679 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:27:52] "[35m[1mPOST /getPromptByName HTTP/1.1[0m" 500 -
2025-03-07 13:27:53,147 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 13:27:53,147 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 13:28:14,188 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:28:14,188 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:28:14,188 - root - ERROR - Error executing query for index '平均票价': Unable to connect to the database: db_wiki
2025-03-07 13:28:14,189 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:28:14] "[35m[1mPOST /getPromptByName HTTP/1.1[0m" 500 -
2025-03-07 13:29:42,292 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 13:30:03,340 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:30:03,340 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:30:03,341 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:30:03] "[31m[1mGET /getIndexTree HTTP/1.1[0m" 400 -
2025-03-07 13:30:03,808 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 13:30:24,860 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:30:24,860 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:30:24,860 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:30:24] "[31m[1mGET /getIndexTree HTTP/1.1[0m" 400 -
2025-03-07 13:30:25,308 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 13:30:46,354 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:30:46,354 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:30:46,355 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:30:46] "[31m[1mGET /getIndexTree HTTP/1.1[0m" 400 -
2025-03-07 13:30:46,828 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 13:31:07,866 - root - ERROR - Error connecting to database db_wiki: 2003: Can't connect to MySQL server on '10.72.81.226:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 13:31:07,867 - root - ERROR - Unable to connect to the database: db_wiki
2025-03-07 13:31:07,867 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 13:31:07] "[31m[1mGET /getIndexTree HTTP/1.1[0m" 400 -
2025-03-07 15:45:43,935 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 15:45:43,936 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\codecs.cpython-38.pyc', reloading
2025-03-07 15:45:43,936 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\aliases.cpython-38.pyc', reloading
2025-03-07 15:45:43,937 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\utf_8.cpython-38.pyc', reloading
2025-03-07 15:45:43,938 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\gbk.cpython-38.pyc', reloading
2025-03-07 15:45:43,938 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\latin_1.cpython-38.pyc', reloading
2025-03-07 15:45:43,939 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\io.cpython-38.pyc', reloading
2025-03-07 15:45:43,939 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-07 15:45:43,941 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\site.cpython-38.pyc', reloading
2025-03-07 15:45:43,942 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\os.cpython-38.pyc', reloading
2025-03-07 15:45:43,942 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\stat.cpython-38.pyc', reloading
2025-03-07 15:45:43,943 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_collections_abc.cpython-38.pyc', reloading
2025-03-07 15:45:43,944 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\ntpath.cpython-38.pyc', reloading
2025-03-07 15:45:43,945 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\genericpath.cpython-38.pyc', reloading
2025-03-07 15:45:43,946 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_sitebuiltins.cpython-38.pyc', reloading
2025-03-07 15:45:43,948 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_bootlocale.cpython-38.pyc', reloading
2025-03-07 15:45:43,949 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\types.cpython-38.pyc', reloading
2025-03-07 15:45:43,950 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 15:45:43,951 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\warnings.cpython-38.pyc', reloading
2025-03-07 15:45:43,952 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\util.cpython-38.pyc', reloading
2025-03-07 15:45:43,953 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-07 15:45:43,953 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\machinery.cpython-38.pyc', reloading
2025-03-07 15:45:43,954 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\contextlib.cpython-38.pyc', reloading
2025-03-07 15:45:43,955 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 15:45:43,956 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\operator.cpython-38.pyc', reloading
2025-03-07 15:45:43,957 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\keyword.cpython-38.pyc', reloading
2025-03-07 15:45:43,957 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\heapq.cpython-38.pyc', reloading
2025-03-07 15:45:43,958 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\reprlib.cpython-38.pyc', reloading
2025-03-07 15:45:43,958 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\functools.cpython-38.pyc', reloading
2025-03-07 15:45:43,971 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\site-packages\\win32\\lib\\__pycache__\\pywin32_bootstrap.cpython-38.pyc', reloading
2025-03-07 15:45:43,992 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\__future__.cpython-38.pyc', reloading
2025-03-07 15:45:43,993 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\inspect.cpython-38.pyc', reloading
2025-03-07 15:45:43,994 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\dis.cpython-38.pyc', reloading
2025-03-07 15:45:43,994 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\opcode.cpython-38.pyc', reloading
2025-03-07 15:45:43,995 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-07 15:45:43,996 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\enum.cpython-38.pyc', reloading
2025-03-07 15:45:43,997 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\linecache.cpython-38.pyc', reloading
2025-03-07 15:45:43,999 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\tokenize.cpython-38.pyc', reloading
2025-03-07 15:45:44,000 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\re.cpython-38.pyc', reloading
2025-03-07 15:45:44,000 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_compile.cpython-38.pyc', reloading
2025-03-07 15:45:44,001 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_parse.cpython-38.pyc', reloading
2025-03-07 15:45:44,002 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_constants.cpython-38.pyc', reloading
2025-03-07 15:45:44,002 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\copyreg.cpython-38.pyc', reloading
2025-03-07 15:45:44,003 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\token.cpython-38.pyc', reloading
2025-03-07 15:45:44,005 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\bisect.cpython-38.pyc', reloading
2025-03-07 15:45:44,005 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\fnmatch.cpython-38.pyc', reloading
2025-03-07 15:45:44,006 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\posixpath.cpython-38.pyc', reloading
2025-03-07 15:45:44,007 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\optparse.cpython-38.pyc', reloading
2025-03-07 15:45:44,008 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\textwrap.cpython-38.pyc', reloading
2025-03-07 15:45:44,009 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\gettext.cpython-38.pyc', reloading
2025-03-07 15:45:44,010 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\locale.cpython-38.pyc', reloading
2025-03-07 15:45:44,012 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\configparser.cpython-38.pyc', reloading
2025-03-07 15:45:44,017 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\signal.cpython-38.pyc', reloading
2025-03-07 15:45:45,065 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 15:45:46,309 - root - INFO - Starting Flask application
2025-03-07 15:45:46,317 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 15:45:46,327 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 15:45:46,383 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 15:46:21,881 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 15:46:21,881 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 15:46:21,883 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 15:46:21,883 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 15:46:22,929 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 15:46:24,162 - root - INFO - Starting Flask application
2025-03-07 15:46:24,169 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 15:46:24,179 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 15:46:24,234 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:03:01,623 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:03:01,624 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:03:01,624 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:03:02,668 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:03:03,962 - root - INFO - Starting Flask application
2025-03-07 16:03:03,970 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:03:03,980 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:03:04,036 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:03:41,721 - root - INFO - Received request data: {'sql': "SELECT flight_date, (RPK / ASK) AS 客座率, (passenger_revenue / pax_num) AS 平均票价 FROM vw_flight_revenue_cost WHERE airline_code_stats IN ('HU','GS') AND flight_date BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY flight_date;"}
2025-03-07 16:04:02,755 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\locales\\eng\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 16:04:02,757 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\locales\\eng\\__pycache__\\client_error.cpython-38.pyc', reloading
2025-03-07 16:04:02,757 - root - ERROR - Error connecting to database db_bi: 2003: Can't connect to MySQL server on '10.72.83.225:3306' (10060 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。)
2025-03-07 16:04:02,757 - root - ERROR - Unable to connect to the database: db_bi
2025-03-07 16:04:02,758 - werkzeug - INFO - 172.16.113.16 - - [07/Mar/2025 16:04:02] "[31m[1mPOST /execute HTTP/1.1[0m" 400 -
2025-03-07 16:04:03,828 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:04:05,724 - root - INFO - Starting Flask application
2025-03-07 16:04:05,734 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:04:05,747 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:04:10,831 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:04:10,929 - root - INFO - Received request data: {'sql': "SELECT flight_date, (RPK / ASK) AS 客座率, (passenger_revenue / pax_num) AS 平均票价 FROM vw_flight_revenue_cost WHERE airline_code_stats IN ('HU','GS') AND flight_date BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY flight_date;"}
2025-03-07 16:04:11,307 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 16:04:11,309 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 16:04:11,310 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\__pycache__\\mysql_native_password.cpython-38.pyc', reloading
2025-03-07 16:04:11,310 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 16:04:12,357 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:04:13,699 - root - INFO - Starting Flask application
2025-03-07 16:04:13,707 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:04:13,720 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:04:13,873 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:04:19,080 - root - INFO - Received request data: {'sql': "SELECT flight_date, (RPK / ASK) AS 客座率, (passenger_revenue / pax_num) AS 平均票价 FROM vw_flight_revenue_cost WHERE airline_code_stats IN ('HU','GS') AND flight_date BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY flight_date;"}
2025-03-07 16:04:19,470 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 16:04:19,470 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 16:04:19,471 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 16:04:20,497 - werkzeug - INFO - 172.16.113.16 - - [07/Mar/2025 16:04:20] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 16:04:26,879 - root - INFO - Received request data: {'sql': "SELECT flight_date, COUNT(*) AS non_profit_flight_count FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date BETWEEN '2022-01-01' AND '2022-01-31' AND (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25 GROUP BY flight_date ORDER BY flight_date;"}
2025-03-07 16:04:27,864 - werkzeug - INFO - 172.16.113.16 - - [07/Mar/2025 16:04:27] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 16:04:31,826 - root - INFO - Received request data: {'sql': "SELECT flight_date, COUNT(*) AS non_profit_flight_count FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND flight_date BETWEEN '2022-01-01' AND '2022-01-31' AND (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25 GROUP BY flight_date ORDER BY flight_date;"}
2025-03-07 16:04:34,299 - werkzeug - INFO - 172.16.113.16 - - [07/Mar/2025 16:04:34] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 16:04:52,180 - root - INFO - Received request data: {'sql': "SELECT DATE_FORMAT(flight_date, '%Y-%m') AS month, COUNT(*) AS non_profit_flight_count FROM vw_flight_revenue_cost WHERE airline_code_stats = 'PN' AND (passenger_revenue + cargo_revenue + ticket_refund_revenue + ticket_rebook_revenue + baggage_revenue) < cost_25 AND YEAR(flight_date) = YEAR(CURDATE()) GROUP BY DATE_FORMAT(flight_date, '%Y-%m');"}
2025-03-07 16:04:52,939 - werkzeug - INFO - 172.16.113.16 - - [07/Mar/2025 16:04:52] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 16:14:56,598 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:14:56,599 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:14:56,599 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:14:57,639 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:14:58,779 - root - INFO - Starting Flask application
2025-03-07 16:14:58,787 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:14:58,796 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:15:02,938 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:15:04,711 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:15:04,711 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:15:04,712 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:15:04,712 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:15:05,749 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:15:07,157 - root - INFO - Starting Flask application
2025-03-07 16:15:07,164 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:15:07,174 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:15:07,224 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:17:18,388 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:18,389 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:19,461 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:17:20,583 - root - INFO - Starting Flask application
2025-03-07 16:17:20,590 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:17:20,598 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:17:21,867 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:17:22,677 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:22,678 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:22,678 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:23,800 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:17:24,945 - root - INFO - Starting Flask application
2025-03-07 16:17:24,952 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:17:24,962 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:17:25,175 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:17:29,337 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:29,338 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:29,338 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:17:30,367 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:17:31,492 - root - INFO - Starting Flask application
2025-03-07 16:17:31,499 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:17:31,508 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:17:33,020 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:30:42,969 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 16:30:43,332 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 16:30:43,333 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 16:30:43,334 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 16:30:43,722 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 16:30:43] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 16:30:45,540 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 16:30:45,541 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 16:30:46,270 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 16:30:46] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 16:42:42,773 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 16:42:44,260 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 16:42:44] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 16:42:46,384 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 16:42:46,384 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 16:42:47,606 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 16:42:47] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 16:42:53,088 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE flight_date >= '2022-01-01' AND flight_date <= '2022-01-31' GROUP BY airline_code_stats;"}
2025-03-07 16:42:58,297 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 16:42:58] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 16:44:09,690 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:44:09,691 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:44:09,691 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:44:10,734 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:44:11,964 - root - INFO - Starting Flask application
2025-03-07 16:44:11,970 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:44:11,980 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:44:12,210 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:45:03,501 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:45:03,502 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:45:03,502 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:45:04,540 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:45:05,659 - root - INFO - Starting Flask application
2025-03-07 16:45:05,665 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:45:05,675 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:45:05,915 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:56:34,179 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-07 16:56:34,181 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\codecs.cpython-38.pyc', reloading
2025-03-07 16:56:34,183 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\aliases.cpython-38.pyc', reloading
2025-03-07 16:56:34,184 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\utf_8.cpython-38.pyc', reloading
2025-03-07 16:56:34,185 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\gbk.cpython-38.pyc', reloading
2025-03-07 16:56:34,187 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\latin_1.cpython-38.pyc', reloading
2025-03-07 16:56:34,189 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\io.cpython-38.pyc', reloading
2025-03-07 16:56:34,190 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-07 16:56:34,192 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\site.cpython-38.pyc', reloading
2025-03-07 16:56:34,194 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\os.cpython-38.pyc', reloading
2025-03-07 16:56:34,195 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\stat.cpython-38.pyc', reloading
2025-03-07 16:56:34,197 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_collections_abc.cpython-38.pyc', reloading
2025-03-07 16:56:34,198 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\ntpath.cpython-38.pyc', reloading
2025-03-07 16:56:34,199 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\genericpath.cpython-38.pyc', reloading
2025-03-07 16:56:34,201 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_sitebuiltins.cpython-38.pyc', reloading
2025-03-07 16:56:34,203 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_bootlocale.cpython-38.pyc', reloading
2025-03-07 16:56:34,204 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\types.cpython-38.pyc', reloading
2025-03-07 16:56:34,282 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:56:35,522 - root - INFO - Starting Flask application
2025-03-07 16:56:35,530 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:56:35,539 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:56:36,988 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:56:46,250 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:56:46,251 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:56:46,251 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:56:47,282 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:56:48,444 - root - INFO - Starting Flask application
2025-03-07 16:56:48,452 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:56:48,461 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:56:49,765 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 16:57:00,034 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:57:00,035 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:57:00,035 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:57:01,068 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:57:02,245 - root - INFO - Starting Flask application
2025-03-07 16:57:02,253 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:57:02,262 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:57:02,333 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:57:02,333 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:57:02,333 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 16:57:03,418 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 16:57:04,602 - root - INFO - Starting Flask application
2025-03-07 16:57:04,609 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 16:57:04,618 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 16:57:04,786 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 17:04:41,142 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:04:41,483 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 17:04:41,484 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 17:04:41,485 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 17:04:41,485 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\mysql\\connector\\plugins\\__pycache__\\mysql_native_password.cpython-38.pyc', reloading
2025-03-07 17:04:41,878 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:04:41] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:04:42,525 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 17:04:43,766 - root - INFO - Starting Flask application
2025-03-07 17:04:43,773 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 17:04:43,783 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 17:04:45,106 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 17:04:45,347 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:04:45,348 - root - INFO - SQL: 
            SELECT DISTINCT
                idx.index_name as indexName, 
                idx.ask_prompt, 
                idx.sql_prompt, 
                idx.foreign_keys,
                tbl.table_name_eng AS table_name,
                tbl.TABLE_ALIAS AS table_desc,
                col.name AS column_name, 
                col.alias AS column_desc,
                col.datalength AS column_len,
                col.type AS column_type
            FROM
                idx_index_prompt idx
                LEFT JOIN idx_index_link_table idxtbl ON idx.id = idxtbl.index_id AND idxtbl.del_flag = 0
                LEFT JOIN idx_source_table_info tbl ON tbl.TABLE_NAME_ENG = idxtbl.table_name
                LEFT JOIN idx_source_table_column_info col ON col.tableid = tbl.TABLE_ID AND col.del_flag = 0
            WHERE idx.index_name = '平均票价' AND idx.del_flag = 0
            ORDER BY idx.index_name, tbl.TABLE_NAME_ENG, col.`position`;
        
2025-03-07 17:04:45,732 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 17:04:45,732 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 17:04:45,733 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 17:04:46,145 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:04:46] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 17:04:50,760 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE flight_date >= '2022-01-01' AND flight_date <= '2022-01-31' GROUP BY airline_code_stats ORDER BY avg_ticket_price DESC;"}
2025-03-07 17:04:52,679 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:04:52] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 17:08:26,465 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\appV1.6.py', reloading
2025-03-07 17:08:26,465 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\appV1.6.py', reloading
2025-03-07 17:08:26,466 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\appV1.6.py', reloading
2025-03-07 17:08:27,587 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 17:08:28,722 - root - INFO - Starting Flask application
2025-03-07 17:08:28,729 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 17:08:28,737 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 17:08:32,750 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:08:32,750 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:08:32,751 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:08:33,329 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 17:08:33,790 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 17:08:34,976 - root - INFO - Starting Flask application
2025-03-07 17:08:34,984 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 17:08:34,992 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 17:08:35,040 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 17:09:00,021 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:09:01,476 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 17:09:01,477 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 17:09:01,478 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 17:09:02,963 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:09:02] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:09:04,950 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:09:09,434 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:09:09] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 17:09:14,008 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE flight_date >= '2022-01-01' AND flight_date <= '2022-01-31' GROUP BY airline_code_stats ORDER BY avg_ticket_price DESC;"}
2025-03-07 17:09:16,044 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:09:16] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 17:13:07,145 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:13:07,825 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:13:07] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:13:10,272 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:13:11,757 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:13:11] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 17:13:15,696 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE flight_date >= '2022-01-01' AND flight_date <= '2022-01-31' GROUP BY airline_code_stats ORDER BY avg_ticket_price DESC;"}
2025-03-07 17:13:20,523 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:13:20] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 17:13:41,502 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:13:42,303 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:13:42] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:13:44,259 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:13:51,089 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:13:51] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 17:13:55,272 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE flight_date >= '2022-01-01' AND flight_date <= '2022-01-31' GROUP BY airline_code_stats ORDER BY avg_ticket_price DESC;"}
2025-03-07 17:13:57,002 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:13:57] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 17:25:45,876 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:25:46,646 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:25:46] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:25:48,726 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:25:50,296 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:25:50] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 17:26:33,604 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:26:33,604 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:26:33,606 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:26:34,638 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 17:26:35,784 - root - INFO - Starting Flask application
2025-03-07 17:26:35,791 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 17:26:35,801 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 17:26:37,380 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 17:26:50,933 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:26:50,933 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:26:50,933 - werkzeug - INFO -  * Detected change in 'D:\\develop\\python\\sql_executer\\app.py', reloading
2025-03-07 17:26:51,972 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-07 17:26:53,095 - root - INFO - Starting Flask application
2025-03-07 17:26:53,105 - werkzeug - WARNING -  * Debugger is active!
2025-03-07 17:26:53,116 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-07 17:26:54,373 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-07 17:27:05,418 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:27:05,712 - mysql.connector - INFO - package: mysql.connector.plugins
2025-03-07 17:27:05,712 - mysql.connector - INFO - plugin_name: mysql_native_password
2025-03-07 17:27:05,714 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-03-07 17:27:06,067 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:27:06] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:27:08,283 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:27:09,950 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:27:09] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 17:27:13,990 - root - INFO - Received request data: {'sql': "SELECT airline_code_stats, SUM(passenger_revenue) / SUM(pax_num) AS avg_ticket_price FROM vw_flight_revenue_cost WHERE flight_date >= '2022-01-01' AND flight_date <= '2022-01-31' GROUP BY airline_code_stats ORDER BY avg_ticket_price DESC;"}
2025-03-07 17:27:15,775 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:27:15] "[37mPOST /execute HTTP/1.1[0m" 200 -
2025-03-07 17:29:26,154 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:29:26,894 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:29:26] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:29:29,685 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:29:32,009 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:29:32] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-07 17:32:08,871 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-07 17:32:09,555 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:32:09] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-07 17:32:11,479 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-07 17:32:12,850 - werkzeug - INFO - 172.16.113.247 - - [07/Mar/2025 17:32:12] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-08 22:19:39,844 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-08 22:19:40,344 - werkzeug - INFO - 172.16.113.247 - - [08/Mar/2025 22:19:40] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-08 22:19:41,907 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-08 22:19:42,884 - werkzeug - INFO - 172.16.113.247 - - [08/Mar/2025 22:19:42] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-08 22:33:14,728 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-08 22:33:15,212 - werkzeug - INFO - 172.16.113.247 - - [08/Mar/2025 22:33:15] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-08 22:33:16,952 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-08 22:33:17,917 - werkzeug - INFO - 172.16.113.247 - - [08/Mar/2025 22:33:17] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-08 22:34:26,422 - root - INFO - GetIndexTree data: SELECT index_name,ask_prompt  FROM idx_index_prompt WHERE del_flag=0
2025-03-08 22:34:26,903 - werkzeug - INFO - 172.16.113.247 - - [08/Mar/2025 22:34:26] "[37mGET /getIndexTree HTTP/1.1[0m" 200 -
2025-03-08 22:34:29,695 - root - INFO - Received request data: [{"indexName":"平均票价","ask_prompt":"提问平均票价时使用该指标"}]
2025-03-08 22:34:30,663 - werkzeug - INFO - 172.16.113.247 - - [08/Mar/2025 22:34:30] "[37mPOST /getPromptByName HTTP/1.1[0m" 200 -
2025-03-10 08:44:49,291 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\aliases.cpython-38.pyc', reloading
2025-03-10 08:44:49,331 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-10 08:44:49,332 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\codecs.cpython-38.pyc', reloading
2025-03-10 08:44:49,333 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\aliases.cpython-38.pyc', reloading
2025-03-10 08:44:49,334 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\utf_8.cpython-38.pyc', reloading
2025-03-10 08:44:49,335 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\gbk.cpython-38.pyc', reloading
2025-03-10 08:44:49,337 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\encodings\\__pycache__\\latin_1.cpython-38.pyc', reloading
2025-03-10 08:44:49,337 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\io.cpython-38.pyc', reloading
2025-03-10 08:44:49,338 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-10 08:44:49,339 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\site.cpython-38.pyc', reloading
2025-03-10 08:44:49,340 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\os.cpython-38.pyc', reloading
2025-03-10 08:44:49,340 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\stat.cpython-38.pyc', reloading
2025-03-10 08:44:49,341 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_collections_abc.cpython-38.pyc', reloading
2025-03-10 08:44:49,342 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\ntpath.cpython-38.pyc', reloading
2025-03-10 08:44:49,343 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\genericpath.cpython-38.pyc', reloading
2025-03-10 08:44:49,344 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_sitebuiltins.cpython-38.pyc', reloading
2025-03-10 08:44:49,345 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\_bootlocale.cpython-38.pyc', reloading
2025-03-10 08:44:49,346 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\types.cpython-38.pyc', reloading
2025-03-10 08:44:49,348 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-10 08:44:49,349 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\warnings.cpython-38.pyc', reloading
2025-03-10 08:44:49,350 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\util.cpython-38.pyc', reloading
2025-03-10 08:44:49,351 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-10 08:44:49,351 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\importlib\\__pycache__\\machinery.cpython-38.pyc', reloading
2025-03-10 08:44:49,352 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\contextlib.cpython-38.pyc', reloading
2025-03-10 08:44:49,353 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\__init__.cpython-38.pyc', reloading
2025-03-10 08:44:49,354 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\operator.cpython-38.pyc', reloading
2025-03-10 08:44:49,355 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\keyword.cpython-38.pyc', reloading
2025-03-10 08:44:49,355 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\heapq.cpython-38.pyc', reloading
2025-03-10 08:44:49,357 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\reprlib.cpython-38.pyc', reloading
2025-03-10 08:44:49,358 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\functools.cpython-38.pyc', reloading
2025-03-10 08:44:49,367 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\site-packages\\win32\\lib\\__pycache__\\pywin32_bootstrap.cpython-38.pyc', reloading
2025-03-10 08:44:49,389 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\__future__.cpython-38.pyc', reloading
2025-03-10 08:44:49,389 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\inspect.cpython-38.pyc', reloading
2025-03-10 08:44:49,390 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\dis.cpython-38.pyc', reloading
2025-03-10 08:44:49,391 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\opcode.cpython-38.pyc', reloading
2025-03-10 08:44:49,392 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\collections\\__pycache__\\abc.cpython-38.pyc', reloading
2025-03-10 08:44:49,392 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\enum.cpython-38.pyc', reloading
2025-03-10 08:44:49,394 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\linecache.cpython-38.pyc', reloading
2025-03-10 08:44:49,394 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\tokenize.cpython-38.pyc', reloading
2025-03-10 08:44:49,395 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\re.cpython-38.pyc', reloading
2025-03-10 08:44:49,396 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_compile.cpython-38.pyc', reloading
2025-03-10 08:44:49,397 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_parse.cpython-38.pyc', reloading
2025-03-10 08:44:49,397 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\sre_constants.cpython-38.pyc', reloading
2025-03-10 08:44:49,398 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\copyreg.cpython-38.pyc', reloading
2025-03-10 08:44:49,399 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\token.cpython-38.pyc', reloading
2025-03-10 08:44:49,401 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\bisect.cpython-38.pyc', reloading
2025-03-10 08:44:49,402 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\fnmatch.cpython-38.pyc', reloading
2025-03-10 08:44:49,403 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\posixpath.cpython-38.pyc', reloading
2025-03-10 08:44:49,403 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\optparse.cpython-38.pyc', reloading
2025-03-10 08:44:49,404 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\textwrap.cpython-38.pyc', reloading
2025-03-10 08:44:49,406 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\gettext.cpython-38.pyc', reloading
2025-03-10 08:44:49,407 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\locale.cpython-38.pyc', reloading
2025-03-10 08:44:49,409 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\configparser.cpython-38.pyc', reloading
2025-03-10 08:44:49,414 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\__pycache__\\signal.cpython-38.pyc', reloading
2025-03-10 08:44:50,452 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-10 08:44:51,623 - root - INFO - Starting Flask application
2025-03-10 08:44:51,631 - werkzeug - WARNING -  * Debugger is active!
2025-03-10 08:44:51,641 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-10 08:44:57,004 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
2025-03-12 05:30:13,794 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\JetBrains\\PyCharm\\plugins\\python\\helpers\\pydev\\_pydevd_bundle\\pydevd_comm.py', reloading
2025-03-12 05:30:13,794 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\python\\Lib\\traceback.py', reloading
2025-03-24 14:55:24,169 - root - INFO - Starting Flask application
2025-03-24 14:55:24,204 - werkzeug - INFO -  * Restarting with windowsapi reloader
2025-03-24 14:55:25,077 - root - INFO - Starting Flask application
2025-03-24 14:55:25,092 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 14:55:25,102 - werkzeug - INFO -  * Debugger PIN: 339-935-125
2025-03-24 14:55:25,385 - werkzeug - INFO -  * Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
