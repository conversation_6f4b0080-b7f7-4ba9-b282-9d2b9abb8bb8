'''
Author: Rock
Date: 2025-07-25 12:25:53
LastEditors: Rock
LastEditTime: 2025-08-05 15:07:20
Description: HTTP RTSP Stream Viewer - Improved Version
'''
from flask import Flask, Response, render_template_string
import cv2
import time
import threading
import queue
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 全局变量
frame_queue = queue.Queue(maxsize=2)
rtsp_url = "rtsp://admin:123456@************:554/video1"
# rtsp_url = "rtsp://admin:hnaXMTpl1nk@*************:6060/stream2"

is_streaming = False

def rtsp_reader():
    """独立线程读取RTSP流"""
    global is_streaming

    # 尝试不同的后端
    backends = [cv2.CAP_FFMPEG, cv2.CAP_GSTREAMER, cv2.CAP_ANY]
    cap = None

    for backend in backends:
        try:
            logger.info(f"尝试使用后端: {backend}")
            cap = cv2.VideoCapture(rtsp_url, backend)

            # 设置连接参数
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            cap.set(cv2.CAP_PROP_FPS, 25)
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))

            # 测试连接
            ret, test_frame = cap.read()
            if ret and test_frame is not None:
                logger.info(f"成功连接RTSP流，使用后端: {backend}")
                break
            else:
                cap.release()
                cap = None
        except Exception as e:
            logger.error(f"后端 {backend} 连接失败: {e}")
            if cap:
                cap.release()
                cap = None

    if cap is None:
        logger.error("所有后端都无法连接RTSP流")
        return

    retry_count = 0
    max_retries = 10

    try:
        while is_streaming:
            ret, frame = cap.read()

            if not ret or frame is None:
                retry_count += 1
                logger.warning(f"读取帧失败，重试次数: {retry_count}/{max_retries}")

                if retry_count >= max_retries:
                    logger.error("达到最大重试次数，重新连接...")
                    cap.release()
                    time.sleep(2)

                    # 重新连接
                    cap = cv2.VideoCapture(rtsp_url)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    retry_count = 0

                time.sleep(0.1)
                continue

            retry_count = 0  # 重置重试计数

            # 调整帧大小以提高性能
            height, width = frame.shape[:2]
            if width > 1280:
                scale = 1280 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                frame = cv2.resize(frame, (new_width, new_height))

            # 将帧放入队列
            if not frame_queue.full():
                frame_queue.put(frame)
            else:
                # 如果队列满了，移除旧帧
                try:
                    frame_queue.get_nowait()
                    frame_queue.put(frame)
                except queue.Empty:
                    pass

    except Exception as e:
        logger.error(f"RTSP读取线程错误: {e}")
    finally:
        if cap:
            cap.release()
        logger.info("RTSP读取线程结束")

def generate_frames():
    """生成视频帧"""
    while True:
        try:
            # 从队列获取帧，超时1秒
            frame = frame_queue.get(timeout=1.0)

            # 编码为JPEG
            ret, buffer = cv2.imencode('.jpg', frame, [
                cv2.IMWRITE_JPEG_QUALITY, 85,
                cv2.IMWRITE_JPEG_OPTIMIZE, 1
            ])

            if not ret:
                continue

            frame_bytes = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

        except queue.Empty:
            # 如果队列为空，发送一个空白帧
            logger.warning("队列为空，发送空白帧")
            blank_frame = cv2.imread('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', cv2.IMREAD_COLOR)
            if blank_frame is None:
                # 创建一个简单的空白帧
                blank_frame = cv2.zeros((480, 640, 3), dtype=cv2.uint8)
                cv2.putText(blank_frame, 'No Signal', (250, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

            ret, buffer = cv2.imencode('.jpg', blank_frame)
            if ret:
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
        except Exception as e:
            logger.error(f"生成帧错误: {e}")
            time.sleep(0.1)

@app.route('/video')
def video():
    return Response(generate_frames(),
                   mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/')
def index():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>RTSP Live Stream</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0f0f0; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; }
            .video-container { text-align: center; margin: 20px 0; }
            .video-stream { max-width: 100%; height: auto; border: 2px solid #ddd; border-radius: 5px; }
            .info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .status { text-align: center; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎥 RTSP Live Stream Viewer</h1>
            <div class="info">
                <strong>流地址:</strong> rtsp://admin:***@*************:6060/stream2<br>
                <strong>状态:</strong> <span id="status">连接中...</span>
            </div>
            <div class="video-container">
                <img src="/video" class="video-stream" alt="Live Stream"
                     onload="updateStatus('已连接')"
                     onerror="updateStatus('连接失败')">
            </div>
        </div>

        <script>
            function updateStatus(status) {
                document.getElementById('status').textContent = status;
            }

            // 定期检查连接状态
            setInterval(function() {
                var img = document.querySelector('.video-stream');
                if (img.complete && img.naturalHeight !== 0) {
                    updateStatus('正常播放');
                } else {
                    updateStatus('连接中断');
                }
            }, 5000);
        </script>
    </body>
    </html>
    '''

@app.route('/status')
def status():
    """返回流状态"""
    return {
        'streaming': is_streaming,
        'queue_size': frame_queue.qsize(),
        'rtsp_url': rtsp_url.replace('hnaXMTpl1nk', '***')  # 隐藏密码
    }

if __name__ == '__main__':
    # 启动RTSP读取线程
    is_streaming = True
    rtsp_thread = threading.Thread(target=rtsp_reader, daemon=True)
    rtsp_thread.start()

    logger.info("启动Flask服务器...")
    try:
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    finally:
        is_streaming = False
        logger.info("服务器关闭")
