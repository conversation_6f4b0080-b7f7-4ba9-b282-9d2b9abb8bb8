import os
from docx import Document
from docx.shared import Pt, RGBColor, Inches
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import textwrap


def collect_java_files(root_dir):
    """收集所有Java文件路径"""
    java_files = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.endswith('.java'):
                java_files.append(os.path.join(dirpath, filename))
    return java_files


def create_code_document(java_files, output_path, max_pages=100):
    """创建包含Java代码的Word文档"""
    doc = Document()

    # 设置全局字体
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Consolas'  # 等宽字体
    font.size = Pt(10)

    # 添加标题
    title = doc.add_heading('Java 代码集合', level=0)
    title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

    # 设置代码样式
    code_style = doc.styles.add_style('CodeStyle', 1)
    code_font = code_style.font
    code_font.name = 'Consolas'
    code_font.size = Pt(9)
    code_font.color.rgb = RGBColor(0, 0, 0)  # 黑色

    # 页面设置
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.5)
        section.bottom_margin = Inches(0.5)
        section.left_margin = Inches(0.5)
        section.right_margin = Inches(0.5)

    page_count = 0
    current_page_lines = 0
    max_lines_per_page = 55  # 根据字体大小估算每页行数

    for file_path in java_files:
        if page_count >= max_pages:
            break

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except:
                print(f"无法读取文件: {file_path}")
                continue

        # 添加文件路径标题
        doc.add_heading(f"文件: {os.path.basename(file_path)}", level=1)
        doc.add_paragraph(f"路径: {file_path}", style='Intense Quote')

        # 添加代码内容
        p = doc.add_paragraph(style='CodeStyle')
        p.add_run(content)

        # 添加分隔线
        doc.add_paragraph().add_run('-' * 80).bold = True

        # 估算页面使用情况
        lines = content.count('\n') + 1
        current_page_lines += lines + 4  # 额外计入标题和分隔线

        # 检查是否超过当前页
        if current_page_lines >= max_lines_per_page:
            page_count += 1
            current_page_lines = 0

    # 保存文档
    doc.save(output_path)
    print(f"文档已生成: {output_path}")
    print(f"包含文件: {len(java_files)}")
    print(f"生成页数: {min(page_count, max_pages)}")


if __name__ == "__main__":
    root_dir = r"D:\develop\HNA\jeecg\jeecg-boot\jeecg-boot-module-system\src\main\java\org\jeecg\modules"
    output_docx = "JavaCodeCollection.docx"

    java_files = collect_java_files(root_dir)
    if not java_files:
        print("未找到任何Java文件")
    else:
        create_code_document(java_files, output_docx, max_pages=500)