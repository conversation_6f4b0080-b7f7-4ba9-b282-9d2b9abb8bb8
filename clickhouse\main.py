from clickhouse_driver import Client
import configparser

# 读取配置文件
config = configparser.ConfigParser()
config.read('config.ini')

# 获取配置
db_config = {
    'host': config['clickhouse']['host'],
    'port': int(config['clickhouse']['port']),
    'user': config['clickhouse']['user'],
    'password': config['clickhouse']['password'],
    'database': config['clickhouse']['database']
}

print(db_config)

# 连接 ClickHouse
client = Client(**db_config)

# 创建表
# create_table_query = '''
# CREATE TABLE IF NOT EXISTS example_table (
#     id UInt32,
#     name String,
#     age UInt8
# ) ENGINE = MergeTree()
# ORDER BY id
# '''
# client.execute(create_table_query)

# 插入数据
# insert_data_query = '''
# INSERT INTO example_table (id, name, age) VALUES
# '''
# data = [(1, 'Alice', 25), (2, 'Bob', 30), (3, 'Charlie', 35)]
# client.execute(insert_data_query, data)

# 查询数据
dbTable = 'log_fltnet_esb_all'
select_query = f'SELECT * FROM {dbTable} LIMIT 10'
result = client.execute(select_query)

# 打印查询结果
for row in result:
    print(row)

# 关闭连接
client.disconnect()