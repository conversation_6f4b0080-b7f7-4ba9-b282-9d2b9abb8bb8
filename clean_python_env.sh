#!/bin/bash

# 脚本功能：彻底删除CentOS 7中的所有Python环境
# 注意：运行此脚本前请确保已备份重要数据

# 检查操作系统版本
check_os_version() {
    local os_version
    
    if [ -f /etc/redhat-release ]; then
        if ! os_version=$(cat /etc/redhat-release); then
            print_warn "读取系统版本信息失败"
            read -p "是否继续执行清理操作? (y/N): " confirm
            if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
                print_info "用户取消操作"
                exit 0
            fi
            return 1
        fi
        
        if [[ $os_version == *"CentOS"* ]]; then
            print_info "检测到CentOS系统: $os_version"
            read -p "是否继续执行清理操作? (y/N): " confirm
            if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
                print_info "用户取消操作"
                exit 0
            fi
        else
            print_warn "非CentOS系统: $os_version"
            read -p "是否继续执行清理操作? (y/N): " confirm
            if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
                print_info "用户取消操作"
                exit 0
            fi
        fi
    else
        print_warn "无法检测操作系统版本"
        read -p "是否继续执行清理操作? (y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_info "用户取消操作"
            exit 0
        fi
    fi
}

# 检查磁盘空间
check_disk_space() {
    local available_space
    local available_mb
    
    if ! available_space=$(df / | awk 'NR==2 {print $4}'); then
        print_warn "获取磁盘空间信息失败"
        return 1
    fi
    
    if ! available_mb=$((available_space / 1024)); then
        print_warn "计算磁盘空间失败"
        return 1
    fi
    
    if [ $available_mb -lt 100 ]; then
        print_warn "警告: 根分区可用空间不足 (${available_mb}MB < 100MB)"
        read -p "是否继续执行清理操作? (y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_info "用户取消操作"
            exit 0
        fi
    fi
}

# 初始化日志文件
init_log() {
    # 创建日志目录
    if ! mkdir -p $(dirname "$LOG_FILE") 2>/dev/null; then
        print_warn "创建日志目录失败"
        return 1
    fi
    
    # 检查日志文件是否可写
    if ! touch "$LOG_FILE" 2>/dev/null; then
        print_warn "创建日志文件失败"
        return 1
    fi
    
    # 添加日志头
    if ! echo "===========================================" >> "$LOG_FILE" 2>/dev/null; then
        print_warn "写入日志文件失败"
        return 1
    fi
    if ! echo "Python环境清理脚本日志" >> "$LOG_FILE" 2>/dev/null; then
        print_warn "写入日志文件失败"
        return 1
    fi
    if ! echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$LOG_FILE" 2>/dev/null; then
        print_warn "写入日志文件失败"
        return 1
    fi
    if ! echo "===========================================" >> "$LOG_FILE" 2>/dev/null; then
        print_warn "写入日志文件失败"
        return 1
    fi
}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志文件
LOG_FILE="/var/log/clean_python_env.log"

# 打印并记录日志函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
    if ! echo "[INFO] $(date): $1" >> "$LOG_FILE" 2>/dev/null; then
        # 忽略日志写入失败，只输出到控制台
        :
    fi
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    if ! echo "[WARN] $(date): $1" >> "$LOG_FILE" 2>/dev/null; then
        # 忽略日志写入失败，只输出到控制台
        :
    fi
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    if ! echo "[ERROR] $(date): $1" >> "$LOG_FILE" 2>/dev/null; then
        # 忽略日志写入失败，只输出到控制台
        :
    fi
}

# 检查root权限
check_root() {
    if [ $EUID -ne 0 ]; then
        print_error "此脚本必须以root权限运行"
        exit 1
    fi
}

# 备份重要配置文件
# find / -name "*python*" -type f -o -type d 2>/dev/null | grep -E "python|Python" | head -n 50
backup_configs() {
    local backup_dir="/tmp/python_env_backup_$(date +%Y%m%d_%H%M%S)"
    if ! mkdir -p "$backup_dir" 2>/dev/null; then
        print_warn "创建备份目录失败: $backup_dir"
        return 1
    else
        print_info "成功创建备份目录: $backup_dir"
    fi
    
    local config_files=(
        "/etc/profile"
        "/etc/bashrc"
        "/root/.bashrc"
        "/root/.bash_profile"
        "/etc/environment"
    )
    
    local backup_success=0
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            if cp "$file" "$backup_dir/" 2>/dev/null; then
                print_info "已备份: $file"
                ((backup_success++))
            else
                print_warn "备份失败: $file"
            fi
        fi
    done
    
    if [ $backup_success -gt 0 ]; then
        print_info "配置文件备份完成，备份目录: $backup_dir"
    else
        print_warn "没有配置文件需要备份或备份全部失败"
    fi
}

# 停止可能正在运行的Python相关服务
stop_python_services() {
    print_info "停止可能正在运行的Python相关服务..."
    # 停止常见的Python服务
    if ! systemctl stop supervisord 2>/dev/null; then
        print_warn "停止supervisord服务失败"
    fi
    if ! systemctl stop gunicorn 2>/dev/null; then
        print_warn "停止gunicorn服务失败"
    fi
    if ! systemctl stop celery 2>/dev/null; then
        print_warn "停止celery服务失败"
    fi
    if ! systemctl stop uwsgi 2>/dev/null; then
        print_warn "停止uwsgi服务失败"
    fi
    if ! systemctl stop nginx 2>/dev/null; then
        print_warn "停止nginx服务失败"
    fi
    
    # 杀死可能正在运行的Python进程，但排除当前脚本进程
    # 获取当前脚本的PID
    SCRIPT_PID=$$
    print_info "当前脚本PID: $SCRIPT_PID"
    
    # 查找Python进程但排除当前脚本
    PYTHON_PIDS=$(pgrep -f python | grep -v $SCRIPT_PID)
    if [ -n "$PYTHON_PIDS" ]; then
        print_info "找到Python进程: $PYTHON_PIDS"
        if ! echo "$PYTHON_PIDS" | xargs kill 2>/dev/null; then
            print_warn "停止python服务失败"
        else
            print_info "成功停止python服务"
        fi
    else
        print_info "未找到需要停止的Python进程"
    fi
    
    # 等待进程结束
    sleep 2
    
    # 再次检查并杀死可能残留的Python进程
    PYTHON_PIDS=$(pgrep -f python | grep -v $SCRIPT_PID)
    if [ -n "$PYTHON_PIDS" ]; then
        print_info "找到残留Python进程: $PYTHON_PIDS"
        if ! echo "$PYTHON_PIDS" | xargs kill -9 2>/dev/null; then
            print_warn "强制停止python服务失败"
        else
            print_info "成功强制停止python服务"
        fi
    else
        print_info "未找到需要强制停止的Python进程"
    fi
    
    print_info "Python相关服务已停止"
}

# 删除通过yum安装的Python包
remove_yum_python() {
    print_info "删除通过yum安装的Python包..."
    
    # 获取所有已安装的Python相关包
    # 只删除明确的Python包，避免删除系统关键组件
    PYTHON_PKGS=$(rpm -qa | grep -E "^python[0-9]*(-devel|-pip|-setuptools|-virtualenv)*$")
    
    if [ -n "$PYTHON_PKGS" ]; then
        print_info "将删除以下Python包:"
        echo "$PYTHON_PKGS"
        
        # 确认删除
        read -p "确认删除以上Python包? (y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_info "跳过删除Python包"
            return
        fi
        
        # 删除Python包
    for pkg in $PYTHON_PKGS; do
        print_info "删除包: $pkg"
        if ! yum remove -y "$pkg" 2>/dev/null; then
            print_warn "删除包失败: $pkg"
        else
            print_info "成功删除包: $pkg"
        fi
    done
    
    # 删除可能残留的Python相关包
    RESIDUAL_PKGS=$(rpm -qa | grep -E "(python|pip|setuptools)" | grep -v "python-libs")
    if [ -n "$RESIDUAL_PKGS" ]; then
        print_info "将删除以下可能残留的Python包:"
        echo "$RESIDUAL_PKGS"
        if ! yum remove -y $RESIDUAL_PKGS 2>/dev/null; then
            print_warn "删除可能残留的Python包失败"
        else
            print_info "成功删除可能残留的Python包"
        fi
    fi
    else
        print_info "未找到通过yum安装的Python包"
    fi
}

# 删除通过编译安装的Python
remove_compiled_python() {
    print_info "删除可能通过编译安装的Python..."
    
    # 常见的编译安装目录
    COMPILED_DIRS=(
        "/usr/local/bin/python*"
        "/usr/local/bin/pip*"
        "/usr/local/bin/easy_install*"
        "/usr/local/lib/python*"
        "/usr/local/include/python*"
        "/opt/python*"
        "/usr/local/share/python*"
        "/usr/local/man/man1/python*"
        "/usr/local/lib/pkgconfig/python*"
        "/usr/local/share/man/man1/python*"
        "/usr/local/share/doc/python*"
        "/usr/local/share/info/python*"
    )
    
    # 添加全局确认变量
    GLOBAL_CONFIRM_COMPILED=""
    for dir in "${COMPILED_DIRS[@]}"; do
        if [ -e "$dir" ]; then
            print_info "找到可能的编译安装文件: $dir"
            # 如果用户还没有做出全局选择，则询问
            if [ -z "$GLOBAL_CONFIRM_COMPILED" ]; then
                read -p "确认删除所有找到的编译安装文件? (y/N/a=全部跳过): " GLOBAL_CONFIRM_COMPILED
            fi
            
            if [ "$GLOBAL_CONFIRM_COMPILED" = "y" ] || [ "$GLOBAL_CONFIRM_COMPILED" = "Y" ]; then
                    print_info "删除: $dir"
                    if ! rm -rf "$dir" 2>/dev/null; then
                        print_warn "删除失败: $dir"
                    else
                        print_info "成功删除: $dir"
                    fi
            elif [ "$GLOBAL_CONFIRM_COMPILED" = "a" ] || [ "$GLOBAL_CONFIRM_COMPILED" = "A" ]; then
                print_info "跳过删除: $dir"
            else
                # 为单个文件确认
                read -p "确认删除 $dir? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    print_info "删除: $dir"
                    if ! rm -rf "$dir" 2>/dev/null; then
                        print_warn "删除失败: $dir"
                    else
                        print_info "成功删除: $dir"
                    fi
                else
                    print_info "跳过删除: $dir"
                fi
            fi
        fi
    done
    
    # 删除可能的编译安装目录
    COMPILED_PREFIXES=(
        "/usr/local"
        "/opt"
    )
    
    for prefix in "${COMPILED_PREFIXES[@]}"; do
        if [ -d "$prefix" ]; then
            # 查找可能的Python安装
            # 添加一个全局确认变量，避免用户需要对每个文件逐一确认
            GLOBAL_CONFIRM_EXE=""
            find "$prefix" -name "python*" -type f -executable 2>/dev/null | while read file; do
                print_info "找到编译安装的Python可执行文件: $file"
                # 如果用户还没有做出全局选择，则询问
                if [ -z "$GLOBAL_CONFIRM_EXE" ]; then
                    read -p "确认删除所有找到的Python可执行文件? (y/N/a=全部跳过): " GLOBAL_CONFIRM_EXE
                fi
                
                if [ "$GLOBAL_CONFIRM_EXE" = "y" ] || [ "$GLOBAL_CONFIRM_EXE" = "Y" ]; then
                    print_info "删除编译安装的Python可执行文件: $file"
                    if ! rm -f "$file" 2>/dev/null; then
                        print_warn "删除失败: $file"
                    else
                        print_info "成功删除: $file"
                    fi
                elif [ "$GLOBAL_CONFIRM_EXE" = "a" ] || [ "$GLOBAL_CONFIRM_EXE" = "A" ]; then
                    print_info "跳过删除: $file"
                else
                    # 为单个文件确认
                    read -p "确认删除 $file? (y/N): " confirm
                    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                        print_info "删除编译安装的Python可执行文件: $file"
                        if ! rm -f "$file" 2>/dev/null; then
                            print_warn "删除失败: $file"
                        else
                            print_info "成功删除: $file"
                        fi
                    else
                        print_info "跳过删除: $file"
                    fi
                fi
            done
            
            # 查找并删除Python相关的符号链接
            find "$prefix" -name "python*" -type l 2>/dev/null | while read link; do
                print_info "找到Python符号链接: $link"
                # 为符号链接确认
                read -p "确认删除 $link? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    print_info "删除Python符号链接: $link"
                    if ! rm -f "$link" 2>/dev/null; then
                        print_warn "删除失败: $link"
                    else
                        print_info "成功删除: $link"
                    fi
                else
                    print_info "跳过删除: $link"
                fi
            done
            
            # 查找可能的Python库目录
            # 添加一个全局确认变量，避免用户需要对每个目录逐一确认
            GLOBAL_CONFIRM_LIB=""
            find "$prefix" -path "*lib*python*" -type d 2>/dev/null | while read dir; do
                print_info "找到编译安装的Python库目录: $dir"
                # 如果用户还没有做出全局选择，则询问
                if [ -z "$GLOBAL_CONFIRM_LIB" ]; then
                    read -p "确认删除所有找到的Python库目录? (y/N/a=全部跳过): " GLOBAL_CONFIRM_LIB
                fi
                
                if [ "$GLOBAL_CONFIRM_LIB" = "y" ] || [ "$GLOBAL_CONFIRM_LIB" = "Y" ]; then
                    print_info "删除编译安装的Python库目录: $dir"
                    if ! rm -rf "$dir" 2>/dev/null; then
                        print_warn "删除失败: $dir"
                    else
                        print_info "成功删除: $dir"
                    fi
                elif [ "$GLOBAL_CONFIRM_LIB" = "a" ] || [ "$GLOBAL_CONFIRM_LIB" = "A" ]; then
                    print_info "跳过删除: $dir"
                else
                    # 为单个目录确认
                    read -p "确认删除 $dir? (y/N): " confirm
                    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                        print_info "删除编译安装的Python库目录: $dir"
                        rm -rf "$dir" 2>/dev/null || true
                    else
                        print_info "跳过删除: $dir"
                    fi
                fi
            done
        fi
    done
}

# 清理Python配置和用户数据
remove_python_user_data() {
    print_info "清理Python配置和用户数据..."
    
    # 需要确认的用户数据目录
    USER_DATA_DIRS=(
        "$HOME/.python_history"
        "$HOME/.pip"
        "$HOME/.pydistutils.cfg"
        "/root/.python_history"
        "/root/.pip"
        "/root/.pydistutils.cfg"
        "/etc/pip.conf"
        "/etc/python*"
    )
    
    # 添加全局确认变量
    GLOBAL_CONFIRM_USER_DATA=""
    for dir in "${USER_DATA_DIRS[@]}"; do
        if [ -e "$dir" ]; then
            print_info "找到Python用户数据: $dir"
            # 如果用户还没有做出全局选择，则询问
            if [ -z "$GLOBAL_CONFIRM_USER_DATA" ]; then
                read -p "确认删除所有找到的Python用户数据? (y/N/a=全部跳过): " GLOBAL_CONFIRM_USER_DATA
            fi
            
            if [ "$GLOBAL_CONFIRM_USER_DATA" = "y" ] || [ "$GLOBAL_CONFIRM_USER_DATA" = "Y" ]; then
                print_info "删除: $dir"
                if ! rm -rf "$dir" 2>/dev/null; then
                    print_warn "删除失败: $dir"
                else
                    print_info "成功删除: $dir"
                fi
            elif [ "$GLOBAL_CONFIRM_USER_DATA" = "a" ] || [ "$GLOBAL_CONFIRM_USER_DATA" = "A" ]; then
                print_info "跳过删除: $dir"
            else
                # 为单个文件确认
                read -p "确认删除 $dir? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    print_info "删除: $dir"
                    if ! rm -rf "$dir" 2>/dev/null; then
                        print_warn "删除失败: $dir"
                    else
                        print_info "成功删除: $dir"
                    fi
                else
                    print_info "跳过删除: $dir"
                fi
            fi
        fi
    done
}

# 删除Python虚拟环境
remove_virtual_envs() {
    print_info "删除Python虚拟环境..."
    
    # 删除常见的虚拟环境目录
    VENV_DIRS=(
        "$HOME/.virtualenvs"
        "/root/.virtualenvs"
        "$HOME/venv"
        "/root/venv"
        "$HOME/.venv"
        "/root/.venv"
    )
    
    # 添加一个全局确认变量，避免用户需要对每个目录逐一确认
    GLOBAL_CONFIRM_VENV=""
    for dir in "${VENV_DIRS[@]}"; do
        if [ -d "$dir" ]; then
            print_info "找到虚拟环境目录: $dir"
            # 如果用户还没有做出全局选择，则询问
            if [ -z "$GLOBAL_CONFIRM_VENV" ]; then
                read -p "确认删除所有找到的预定义虚拟环境目录? (y/N/a=全部跳过): " GLOBAL_CONFIRM_VENV
            fi
            
            if [ "$GLOBAL_CONFIRM_VENV" = "y" ] || [ "$GLOBAL_CONFIRM_VENV" = "Y" ]; then
                print_info "删除虚拟环境目录: $dir"
                if ! rm -rf "$dir" 2>/dev/null; then
                    print_warn "删除失败: $dir"
                else
                    print_info "成功删除: $dir"
                fi
            elif [ "$GLOBAL_CONFIRM_VENV" = "a" ] || [ "$GLOBAL_CONFIRM_VENV" = "A" ]; then
                print_info "跳过删除: $dir"
            else
                # 为单个目录确认
                read -p "确认删除 $dir? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    print_info "删除虚拟环境目录: $dir"
                    if ! rm -rf "$dir" 2>/dev/null; then
                        print_warn "删除失败: $dir"
                    else
                        print_info "成功删除: $dir"
                    fi
                else
                    print_info "跳过删除: $dir"
                fi
            fi
        fi
    done
    
    # 查找并删除系统中的虚拟环境
    # 添加一个全局确认变量，避免用户需要对每个目录逐一确认
    GLOBAL_CONFIRM_FOUND_VENV=""
    find / -type d -name "venv" -o -name ".venv" -o -name "env" -o -name ".env" 2>/dev/null | while read dir; do
        # 检查是否为虚拟环境
        if [ -f "$dir/bin/activate" ] || [ -f "$dir/Scripts/activate" ]; then
            print_info "找到虚拟环境: $dir"
            # 如果用户还没有做出全局选择，则询问
            if [ -z "$GLOBAL_CONFIRM_FOUND_VENV" ]; then
                read -p "确认删除所有找到的虚拟环境? (y/N/a=全部跳过): " GLOBAL_CONFIRM_FOUND_VENV
            fi
            
            if [ "$GLOBAL_CONFIRM_FOUND_VENV" = "y" ] || [ "$GLOBAL_CONFIRM_FOUND_VENV" = "Y" ]; then
                print_info "删除虚拟环境: $dir"
                if ! rm -rf "$dir" 2>/dev/null; then
                    print_warn "删除失败: $dir"
                else
                    print_info "成功删除: $dir"
                fi
            elif [ "$GLOBAL_CONFIRM_FOUND_VENV" = "a" ] || [ "$GLOBAL_CONFIRM_FOUND_VENV" = "A" ]; then
                print_info "跳过删除: $dir"
            else
                # 为单个目录确认
                read -p "确认删除 $dir? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    print_info "删除虚拟环境: $dir"
                    if ! rm -rf "$dir" 2>/dev/null; then
                        print_warn "删除失败: $dir"
                    else
                        print_info "成功删除: $dir"
                    fi
                else
                    print_info "跳过删除: $dir"
                fi
            fi
        fi
    done
    
    # 查找并删除可能的虚拟环境符号链接
    find / -type l -name "venv" -o -name ".venv" -o -name "env" -o -name ".env" 2>/dev/null | while read link; do
        print_info "找到虚拟环境符号链接: $link"
        # 为符号链接确认
        read -p "确认删除 $link? (y/N): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            print_info "删除虚拟环境符号链接: $link"
            if ! rm -f "$link" 2>/dev/null; then
                print_warn "删除失败: $link"
            else
                print_info "成功删除: $link"
            fielse
                print_info "成功删除: $link"
            fi
        else
            print_info "跳过删除: $link"
        fi
    done
}

# 清理Python缓存和配置
clean_python_cache() {
    print_info "清理Python缓存和配置..."
    
    # 删除用户目录中的Python缓存
    PYCACHE_DIRS=(
        "$HOME/.cache/pip"
        "$HOME/.pip"
        "/root/.cache/pip"
        "/root/.pip"
        "$HOME/.local/lib/python*"
        "/root/.local/lib/python*"
        "$HOME/.local/bin/pip*"
        "/root/.local/bin/pip*"
    )
    
    for dir in "${PYCACHE_DIRS[@]}"; do
        if [ -d "$dir" ] || [ -f "$dir" ]; then
            print_info "删除Python缓存: $dir"
            if ! rm -rf "$dir" 2>/dev/null; then
                print_warn "删除失败: $dir"
            else
                print_info "成功删除: $dir"
            fielse
                print_info "成功删除: $dir"
            fi
        fi
    done
    
    # 删除系统范围的Python缓存
    if ! rm -rf /var/cache/pip 2>/dev/null; then
        print_warn "删除失败: /var/cache/pip"
    else
        print_info "成功删除: /var/cache/pip"
    fi
    if ! rm -rf /usr/share/pip 2>/dev/null; then
        print_warn "删除失败: /usr/share/pip"
    else
        print_info "成功删除: /usr/share/pip"
    fi
    if ! rm -rf /usr/local/share/pip 2>/dev/null; then
        print_warn "删除失败: /usr/local/share/pip"
    else
        print_info "成功删除: /usr/local/share/pip"
    fi
    
    # 删除可能的Python配置文件
    PY_CONFIG_FILES=(
        "$HOME/.pydistutils.cfg"
        "/root/.pydistutils.cfg"
        "$HOME/.pypirc"
        "/root/.pypirc"
        "/usr/local/bin/pip*"
        "/usr/local/bin/easy_install*"
        "/usr/bin/pip*"
        "/usr/bin/easy_install*"
    )
    
    for file in "${PY_CONFIG_FILES[@]}"; do
        if [ -f "$file" ]; then
            print_info "删除Python配置文件: $file"
            if ! rm -f "$file" 2>/dev/null; then
                print_warn "删除失败: $file"
            else
                print_info "成功删除: $file"
            fielse
                print_info "成功删除: $file"
            fi
        fi
    done
    
    # 删除可能的Python可执行文件
    find /usr/local/bin -name "python*" -type f -executable 2>/dev/null | while read file; do
        print_info "删除Python可执行文件: $file"
        if ! rm -f "$file" 2>/dev/null; then
            print_warn "删除失败: $file"
        else
            print_info "成功删除: $file"
        fielse
            print_info "成功删除: $file"
        fielse
            print_info "成功删除: $file"
        fi
    done
    
    find /usr/local/bin -name "pip*" -type f -executable 2>/dev/null | while read file; do
        print_info "删除pip可执行文件: $file"
        if ! rm -f "$file" 2>/dev/null; then
            print_warn "删除失败: $file"
        else
            print_info "成功删除: $file"
        fi
    done
}

# 清理环境变量
clean_env_vars() {
    print_info "清理环境变量..."
    
    # 备份原始配置文件
    CONFIG_FILES=(
        "/etc/profile"
        "/etc/bashrc"
        "$HOME/.bashrc"
        "$HOME/.bash_profile"
        "/root/.bashrc"
        "/root/.bash_profile"
    )
    
    for file in "${CONFIG_FILES[@]}"; do
        if [ -f "$file" ]; then
            print_info "检查配置文件: $file"
            # 显示包含Python路径的行
            PYTHON_LINES=$(grep -E '(python|PYTHONPATH|PYTHONHOME)' "$file" 2>/dev/null || true)
            if [ -n "$PYTHON_LINES" ]; then
                print_info "找到以下包含Python的行:"
                echo "$PYTHON_LINES"
                # 确认删除
                read -p "确认从 $file 中删除以上行? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    print_info "从 $file 中删除Python相关行"
                    if ! sed -i '/python/d' "$file" 2>/dev/null; then
                        print_warn "删除python相关行失败: $file"
                    else
                        print_info "成功删除python相关行: $file"
                    fi
                    if ! sed -i '/PYTHONPATH/d' "$file" 2>/dev/null; then
                        print_warn "删除PYTHONPATH相关行失败: $file"
                    else
                        print_info "成功删除PYTHONPATH相关行: $file"
                    fi
                    if ! sed -i '/PYTHONHOME/d' "$file" 2>/dev/null; then
                        print_warn "删除PYTHONHOME相关行失败: $file"
                    else
                        print_info "成功删除PYTHONHOME相关行: $file"
                    fi
                    if ! sed -i '/pip/d' "$file" 2>/dev/null; then
                        print_warn "删除pip相关行失败: $file"
                    else
                        print_info "成功删除pip相关行: $file"
                    fi
                else
                    print_info "跳过修改: $file"
                fi
            else
                print_info "未找到Python相关行: $file"
            fi
        fi
    done
}

# 重新生成系统库缓存
regenerate_ldconfig() {
    print_info "重新生成系统库缓存"
    if ! ldconfig 2>/dev/null; then
        print_warn "重新生成系统库缓存失败"
    else
        print_info "成功重新生成系统库缓存"
    fi
}

# 删除系统自带的Python
remove_system_python() {
    print_info "删除系统自带的Python..."
    
    # 确认删除系统Python
    read -p "确认删除系统自带的Python? 这可能会影响系统稳定性! (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_info "跳过删除系统Python"
        return
    fi
    
    # 删除系统Python相关包
    SYSTEM_PYTHON_PKGS=$(rpm -qa | grep -E "^python-[0-9]" | grep -v "python-libs")
    if [ -n "$SYSTEM_PYTHON_PKGS" ]; then
        print_info "将删除以下系统Python包:"
        echo "$SYSTEM_PYTHON_PKGS"
        if ! yum remove -y $SYSTEM_PYTHON_PKGS 2>/dev/null; then
            print_warn "删除系统Python包失败"
        else
            print_info "成功删除系统Python包"
        fi
    else
        print_info "未找到系统Python包"
    fi
    
    # 删除系统Python链接
    if ! rm -f /usr/bin/python 2>/dev/null; then
        print_warn "删除 /usr/bin/python 失败"
    else
        print_info "成功删除 /usr/bin/python"
    fi
    if ! rm -f /usr/bin/python2 2>/dev/null; then
        print_warn "删除 /usr/bin/python2 失败"
    else
        print_info "成功删除 /usr/bin/python2"
    fi
    if ! rm -f /usr/bin/python2.7 2>/dev/null; then
        print_warn "删除 /usr/bin/python2.7 失败"
    else
        print_info "成功删除 /usr/bin/python2.7"
    fi
    if ! rm -f /usr/bin/python3 2>/dev/null; then
        print_warn "删除 /usr/bin/python3 失败"
    else
        print_info "成功删除 /usr/bin/python3"
    fi
    if ! rm -f /usr/bin/python3.* 2>/dev/null; then
        print_warn "删除 /usr/bin/python3.* 失败"
    else
        print_info "成功删除 /usr/bin/python3.*"
    fi
    
    # 删除可能的系统Python目录
    if ! rm -rf /usr/lib/python2.7 2>/dev/null; then
        print_warn "删除 /usr/lib/python2.7 失败"
    else
        print_info "成功删除 /usr/lib/python2.7"
    fi
    if ! rm -rf /usr/lib64/python2.7 2>/dev/null; then
        print_warn "删除 /usr/lib64/python2.7 失败"
    else
        print_info "成功删除 /usr/lib64/python2.7"
    fi
    if ! rm -rf /usr/lib/python3* 2>/dev/null; then
        print_warn "删除 /usr/lib/python3* 失败"
    else
        print_info "成功删除 /usr/lib/python3*"
    fi
    if ! rm -rf /usr/lib64/python3* 2>/dev/null; then
        print_warn "删除 /usr/lib64/python3* 失败"
    else
        print_info "成功删除 /usr/lib64/python3*"
    fi
    if ! rm -rf /usr/local/lib/python* 2>/dev/null; then
        print_warn "删除 /usr/local/lib/python* 失败"
    else
        print_info "成功删除 /usr/local/lib/python*"
    fi
    
    # 删除可能的Python配置文件
    if ! rm -f /etc/python* 2>/dev/null; then
        print_warn "删除 /etc/python* 失败"
    else
        print_info "成功删除 /etc/python*"
    fi
    
    # 删除用户目录中的Python相关文件
    if ! rm -rf $HOME/.local/lib/python* 2>/dev/null; then
        print_warn "删除 $HOME/.local/lib/python* 失败"
    else
        print_info "成功删除 $HOME/.local/lib/python*"
    fi
    if ! rm -rf /root/.local/lib/python* 2>/dev/null; then
        print_warn "删除 /root/.local/lib/python* 失败"
    else
        print_info "成功删除 /root/.local/lib/python*"
    fi
    
    # 删除其他可能的Python组件
    if ! rm -rf /usr/share/python* 2>/dev/null; then
        print_warn "删除 /usr/share/python* 失败"
    else
        print_info "成功删除 /usr/share/python*"
    fi
    if ! rm -rf /usr/libexec/python* 2>/dev/null; then
        print_warn "删除 /usr/libexec/python* 失败"
    else
        print_info "成功删除 /usr/libexec/python*"
    fi
    if ! rm -rf /usr/lib/pkgconfig/python* 2>/dev/null; then
        print_warn "删除 /usr/lib/pkgconfig/python* 失败"
    else
        print_info "成功删除 /usr/lib/pkgconfig/python*"
    fi
    
    print_info "系统Python删除完成"
}

# 主函数
main() {
    # 初始化日志文件
    init_log
    
    print_info "===== 开始彻底删除Python环境 ====="
    
    # 检查操作系统版本
    check_os_version
    
    # 检查磁盘空间
    check_disk_space
    
    # 检查root权限
    check_root
    
    # 备份重要配置文件
    print_info "建议在执行此操作前备份重要配置文件"
    read -p "是否现在备份重要配置文件? (Y/n): " backup_confirm
    if [ "$backup_confirm" != "n" ] && [ "$backup_confirm" != "N" ]; then
        backup_configs
    else
        print_info "跳过配置文件备份"
    fi
    
    # 确认操作
    print_warn "此操作将彻底删除系统中的所有Python环境，包括系统自带的Python!"
    print_warn "删除后可能影响系统其他依赖Python的组件!"
    print_warn "建议在执行此操作前备份重要数据!"
    read -p "是否继续? (y/N): " confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_info "操作已取消"
        exit 0
    fi
    
    # 再次确认
    print_warn "请再次确认，此操作不可逆!"
    read -p "是否继续? (y/N): " confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        print_info "操作已取消"
        exit 0
    fi
    
    # 停止Python相关服务
    stop_python_services
    
    # 删除通过yum安装的Python
    remove_yum_python
    
    # 删除编译安装的Python
    remove_compiled_python
    
    # 删除虚拟环境
    remove_virtual_envs
    
    # 清理缓存
    clean_python_cache
    
    # 清理Python配置和用户数据
    remove_python_user_data
    
    # 清理环境变量
    clean_env_vars
    
    # 删除系统自带的Python
    remove_system_python
    
    # 重新生成库缓存
    regenerate_ldconfig
    
    print_info "===== Python环境清理完成 ====="
    print_info "建议重启系统以确保所有更改生效"
    
    # 显示日志文件位置
    if [ -f "$LOG_FILE" ]; then
        print_info "执行日志已保存到: $LOG_FILE"
        print_info "如需查看详细执行过程，请使用以下命令:"
        print_info "  cat $LOG_FILE"
    fi
}

# 执行主函数
main "$@"