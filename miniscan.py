import scapy.all as scapy
import socket
import concurrent.futures
import mysql.connector
from mysql.connector import Error

# 获取主机名的辅助函数
def get_hostname(ip):
    try:
        return socket.gethostbyaddr(ip)[0]
    except (socket.herror, socket.gaierror):
        return '未知'

# 扫描局域网内在线的设备并尝试获取主机名
def scan(ip_range):
    online_devices = []
    for ip in ip_range:
        arp_request = scapy.ARP(pdst=ip)
        broadcast = scapy.Ether(dst="ff:ff:ff:ff:ff:ff")
        arp_request_broadcast = broadcast / arp_request
        answered_list = scapy.srp(arp_request_broadcast, timeout=1, verbose=False)[0]

        with concurrent.futures.ThreadPoolExecutor() as executor:
            ip_to_hostname = {element[1].psrc: executor.submit(get_hostname, element[1].psrc) for element in answered_list}

            for element in answered_list:
                ip = element[1].psrc
                mac = element[1].hwsrc
                hostname = ip_to_hostname[ip].result()

                online_devices.append({
                    "ip": ip,
                    "mac": mac,
                    "hostname": hostname
                })

    return online_devices

# 将设备信息存储到 MySQL 数据库
def store_to_db(devices):
    try:
        # 连接 MySQL 数据库
        connection = mysql.connector.connect(
            host='**************',  # 数据库主机
            database='devices',  # 数据库名称
            user='fms_app',  # 数据库用户名
            password='B$u#kv$$zx13xz!J',  # 数据库密码
            charset='utf8mb4',  # 设置字符编码为 UTF-8
            port=3307  # 设置数据库端口，默认是 3306
        )

        if connection.is_connected():
            cursor = connection.cursor()

            # 插入设备信息到数据库
            for device in devices:
                insert_query = """INSERT INTO devices (ip, mac, hostname) 
                                  VALUES (%s, %s, %s)"""
                data = (device['ip'], device['mac'], device['hostname'])
                cursor.execute(insert_query, data)

            # 提交事务
            connection.commit()
            print("设备信息已成功存储到数据库！")

    except Error as e:
        print(f"数据库连接错误: {e}")

    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

# 设置局域网的 IP 范围（根据你的网络配置修改）
ip_range = [f"172.16.113.{i}" for i in range(10, 160)]

# 执行扫描并保存结果到数据库
devices = scan(ip_range)
if devices:
    store_to_db(devices)
